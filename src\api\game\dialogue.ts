import { Input } from '../core/input/input'
import { MenuOpcode } from '../model/menuOpcode'
import { Tile } from '../model/tile'
import { getCache } from '../utils/cache'
import { Time } from '../utils/time'
import { log } from '../utils/utils'
import { Npc } from '../wrappers/npc'
import { Npcs } from './npcs'
import { Walking } from './walking'
import { Widgets } from './widgets'

export class Dialogue {
    private static lastDialogueTime = 0
    public static readonly DIALOG_OPTION_GROUP_ID = 219
    private static readonly WidgetsClickContinue: number[][] = [/*[10616877],*/ [12648448], [231, 3], [229, 4], [217, 3], [193, 3], [11, 4], [233, 3], [162, 45], [193, 0, 2], [633, 0, 2]]

    // public static mustClickContinue(): boolean {
    //     log("Widgets.get(10616877).isRendered", Widgets.get(10616877).isRendered)
    //     return this.isOpen() && Widgets.get(10616877).isRendered
    // }

    static contains(str: string) {
        return this.getContent().includes(str)
    }

    public static getContinueWidgetNoText() {
        return this.WidgetsClickContinue.map((ids) => {
            switch (ids.length) {
                case 1:
                    return Widgets.get(ids[0])
                case 3:
                    return Widgets.getByRootId(ids[0], ids[1], ids[2])
                default:
                    return Widgets.getByRootId(ids[0], ids[1])
            }
        }).find((w) => w != null && w.isRendered)
    }

    public static canClickContinue(): boolean {
        return this.getContinueWidgetNoText() !== null// || this.mustClickContinue()
    }

    public static continueDialogue() {
        if (!this.canClickContinue()) return
        if (getCache('continue_dialogue')) return

        Input.space()
        Time.sleep(10, 100)
    }

    public static goNextPredicate(predicate?: (option: string) => boolean): boolean {
        predicate ??= () => true

        Dialogue.continueDialogue()
        return this.clickByPredicate(predicate)
    }

    private static clickedOptions: string[] = []

    public static goNextLeastClicked(...optionsToClick: string[]): boolean {
        Dialogue.continueDialogue()

        let options = this.getOptions().map((o, idx) => ({ index: idx, option: o }))

        options = options.filter((o) => optionsToClick.some((o2) => o.option.toLowerCase().startsWith(o2.toLowerCase())))

        if (options.length === 0) {
            return false
        }

        const optionClickCounts: { [key: string]: number } = {}

        this.clickedOptions.forEach((opt) => {
            optionClickCounts[opt] = (optionClickCounts[opt] || 0) + 1
        })

        let leastClickedOption = options[0]

        for (const option of options) {
            const count = optionClickCounts[option.option] || 0

            const leastCount = optionClickCounts[leastClickedOption.option] || 0

            if (count < leastCount) {
                leastClickedOption = option
            }
        }

        this.clickedOptions.push(leastClickedOption.option)
        return this.clickByPredicate((option) => option.toLowerCase().startsWith(leastClickedOption.option.toLowerCase()))
    }

    public static goNext(...options: string[]): boolean {
        Dialogue.continueDialogue()
        log('Options to click: ' + options)
        return this.clickByPredicate((option) => options.some((o2) => option.toLowerCase().startsWith(o2.toLowerCase())))
    }

    public static getOptions(): string[] {
        const options: string[] = []
        const widget = Widgets.getByRootId(this.DIALOG_OPTION_GROUP_ID, 1)

        if (widget?.children) {
            for (let i = 0; i < widget.children.getChildrenCount(); i++) {
                try {
                    const child = widget.children.getAt(i)
                    let option = child?.text
                    if (option?.length > 0) {
                        options.push(option)
                    }
                } catch {}
            }
        }

        if (options.length > 0) {
            options.shift() //remove the first option which is the title
        }

        return options
    }

    public static clickByString(containsString: string): boolean {
        return this.clickByPredicate((option) => option.toLowerCase().includes(containsString.toLowerCase()))
    }

    public static containsOption(containsString: string): boolean {
        return this.getOptions().find((option) => option.toLowerCase().includes(containsString.toLowerCase())) != null
    }

    public static clickByPredicate(predicate: (option: string) => boolean): boolean {
        const options = this.getOptions()
        log('Options: ' + options)

        for (let i = 0; i < options.length; i++) {
            if (!predicate(options[i])) continue
            this.clickedOptions.push(options[i])
            log('Typing: ' + (i + 1) + ' option name ' + options[i])
            Input.type('{' + (i + 1) + '}')
            return true
        }
        return false
    }

    public static getContent(): string {
        return (
            Widgets.getByRootId(231, 6)?.text ||
            Widgets.getByRootId(217, 6)?.text ||
            Widgets.getByRootId(193, 2)?.text ||
            Widgets.getByRootId(633, 3)?.text ||
            Widgets.getByRootId(11, 2)?.text ||
            Widgets.getByRootId(229, 3)?.text ||
            ''
        )
    }

    public static isOpen(): boolean {
        const isInDialogue = this.getContent().length > 0 || this.getOptions().length > 0
        

        if (isInDialogue) this.lastDialogueTime = Date.now()
        return Date.now() - this.lastDialogueTime < 1600
    }

    public static talkToNpcById(npcId: number, walk: boolean = true, tile: Tile = null): boolean {
        return this.talkTo(Npcs.getById(npcId), walk, tile)
    }

    // public static talkToNpcByName(npcName: string, walk: boolean = true): boolean {
    //     return this.talkTo(Npcs.byNameEquals(npcName), walk);
    // }

    static talkTo(npc: Npc | null, walk: boolean = true, tile: Tile = null): boolean {
        if (tile != null && !Walking.walkTo(tile, 10)) {
            log("Did not walk yer to npc")
            return false
        }

        
        if (Dialogue.isOpen()) {
            log("Is open already")
            return true
        }
        
        if (npc != null) {
            log("Npc is not null")
            if (walk && !npc.tile.isReachable()) {
                log("Walking to npc tile")
                Walking.walkTo(npc.tile, 0)
            } else {
                log("Clicking npc")
                npc.click(MenuOpcode.NPC_FIRST_OPTION)
                if (Time.sleep(6000, 1000, () => Dialogue.isOpen())) {
                    return true
                }
            }
        }
        log("Returning talkTo: ", this.isOpen())
        return this.isOpen()
    }
}
