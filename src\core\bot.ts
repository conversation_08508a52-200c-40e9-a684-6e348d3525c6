import { NativeInput } from '../api/core/input/input'
import { LowCpu } from '../api/core/lowCpu'
import { ClientTickEvent, EventBus, GameMessageEvent, ServerTickEvent, SoundEffectEvent } from '../api/core/script/game-events/eventBus'
import { <PERSON>riptHandler } from '../api/core/script/scriptHandler'
import { GrandExchange } from '../api/game/grandExchange'
import { Widgets } from '../api/game/widgets'
import { Point } from '../api/model/point'
import { Schedule } from '../api/model/schedule'
import { getCache, putCache } from '../api/utils/cache'
import { CameraMover } from '../api/utils/cameraMover'
import { Redis } from '../api/utils/redis'
import { logSentry } from '../api/utils/sentry'
import { Time } from '../api/utils/time'
import { javaPerform, javaPerformNow, killCurrentProcess, log, readJagString, runOnUtilityThread, startLoop, toast } from '../api/utils/utils'
import { Client } from '../api/wrappers/client'
import { CachedCollisionFlagsContainer } from '../api/wrappers/collisionFlagsContainer'
import { Game } from '../api/wrappers/game'
import { Menu, MenuAction } from '../api/wrappers/menu'
import { Player } from '../api/wrappers/player'
import { WidgetsGroup } from '../api/wrappers/widget'
import { BotSettings } from '../botSettings'
import { VarbitDefinitions } from '../data/VarbitDefinitions'
import { ItemDefinitions } from '../data/itemDefinitions'
import { Names } from '../data/names'
import { NpcDefinitions } from '../data/npcDefinitions'
import { ObjectDefinitions } from '../data/objectDefinitions'
import { WebData } from '../data/webData'
import { Offsets } from '../offsets'
import { Platform } from '../platform'
import { ProxyInjector } from '../proxyInject'
import { CrashMonitor } from './background-tasks/crashMonitor'
import { FileAccessWatcher } from './background-tasks/fileAccessWatcher'
import { BatterySimulator, FingerprintSpoofer } from './background-tasks/fingerprintSpoofer'
import { GameStateMonitor } from './background-tasks/gameStateMonitor'
import { IpAddressChecker } from './background-tasks/ipAddressChecker'
import { LowCpuSetter } from './background-tasks/lowCpuSetter'
import { MessedAccountGuard } from './background-tasks/messedAccountGuard'
import { OnlineDataSender } from './background-tasks/onlineDataSender'
import { ScriptActiveTimeMonitor } from './background-tasks/scriptActiveTimeMonitor'
import { ScriptStarter } from './background-tasks/scriptStarter'
import { SessionLimiter } from './background-tasks/sessionLimiter'
import { Debuggers } from './debuggers'

export class Bot {
    static version: string

    static scriptHandler: ScriptHandler
    static lastGameCycle: number
    static menuActionToInvoke: MenuAction
    static initialized: boolean
    private static functionsToInvokeOnGameThread: Function[] = []

    static eventBus: EventBus = new EventBus()

    static allocations: NativePointer[] = []

    static init() {
        log('Initializing OSRSBot')
        const json: any = require('../../app.json')
        Bot.version = json.version

        javaPerformNow(() => {
            log('Injecting hooks')
             this.initTest()
            this.initSoundEffects()
            this.initLowCpu()
            this.initMenuInvokeInterceptor()
            this.initClientTick()
            this.initDrawWidgets()
            this.initOnMessageListener()
            this.initHealthBars()
            this.initInputEvents()
            this.initOnHardwareDataRequested()
            // this.initCollisionCaching()
            // this.initBackButtonPrevent()
            CameraMover.init()
            Schedule.init()

          

            this.scriptHandler = new ScriptHandler()
            log('Starting background tasks')
            this.initBackgroundTasks()

            FingerprintSpoofer.onBotInitialized()

            log('Starting with account: ' + ScriptStarter.getAccountFromStartArguments())
            this.initialized = true
        })
    }

    static initInputEvents() {
        // Interceptor.attach(Game.base.add(Offsets.Native.onInputEvent), {
        //     // Replace with the actual base address if ASLR is enabled
        //     onEnter: function (args) {
        //         // console.log('[+] Entered 0xe4b48')
        //         // console.log('InputEvent:', args[0], args[1], ' | ', args[0].readPointer(), ' != ', Game.base.add(0x1e4d800).readPointer())
        //         const keyEvent = args[1]
        //         // NativeInput.printInputEvent(keyEvent, args[0])
        //     },
        //     onLeave: function (retval) {},
        // })

        if (Platform.isArm64) {
            javaPerform(function () {
                var AndroidKeyboard = Java.use('com.jagex.android.AndroidKeyboard')

                //@ts-ignore
                AndroidKeyboard.show.overload('int', 'java.lang.String', 'int', 'boolean', 'boolean').implementation = function (p0, p1, p2, p3, p4) {
                    console.log('Intercepted show method')
                    console.log('  p0 (int): ' + p0)
                    console.log('  p1 (String): ' + p1)
                    console.log('  p2 (int): ' + p2)
                    console.log('  p3 (boolean): ' + p3)
                    console.log('  p4 (boolean): ' + p4)
                }
            })
        }

        // Replace with the actual name of the library where this function resides
        // You can find this using `Process.enumerateModules()` in Frida.

        // javaPerform(function () {
        //     Interceptor.attach(Game.base.add(0xefa38), {
        //         onEnter: function (args) {
        //             console.log('--- Intercepted: sub_efa38 ---')
        //             console.log('  arg1:', args[0]) // Pointer to the first argument
        //             console.log('  arg2:', args[1]) // Second argument as int16_t
        //         },
        //         onLeave: function (retval) {},
        //     })

        //     console.log('Interceptor attached to sub_efa38')
        // })

        // javaPerform(function () {
        //     Interceptor.attach(Game.base.add(0xf796c), {
        //         onEnter: function (args) {
        //             console.log('--- Intercepted: sub_f796c ---')
        //             console.log('  arg1:', args[0]) // Pointer to int64_t
        //             console.log('  arg2:', args[1]) // Pointer to void*
        //             console.log('  arg3:', args[2]) // Pointer to int64_t (in x8 register)
        //         },
        //         onLeave: function (retval) {
        //             // Log return value if needed
        //             // console.log('  Return value:', retval)
        //         },
        //     })

        //     console.log('Interceptor attached to sub_f796c')
        // })
        // javaPerform(function () {
        //     // Interceptor for sub_164da0
        //     Interceptor.attach(Game.base.add(0x164da0), {
        //         onEnter: function (args) {
        //             console.log('--- Intercepted: sub_164da0 ---')
        //             console.log('  arg1:', args[0]) // void* pointer
        //             console.log('  arg2:', args[1]) // int32_t value
        //         },
        //         onLeave: function (retval) {
        //             console.log('  Return value:', retval) // uint64_t return value
        //         },
        //     })
        //     console.log('Interceptor attached to sub_164da0')

        //     // Interceptor for sub_26e0ac
        //     Interceptor.attach(Game.base.add(0x26e0ac), {
        //         onEnter: function (args) {
        //             console.log('--- Intercepted: sub_26e0ac ---')
        //             console.log('  arg1:', args[0]) // void* pointer
        //             console.log('  arg2:', args[1]) // int32_t value
        //         },
        //         onLeave: function (retval) {
        //             console.log('  Return value:', retval) // uint64_t return value
        //         },
        //     })
        //     console.log('Interceptor attached to sub_26e0ac')
        // })

        // javaPerform(function () {
        //     const targetAddress = new NativeFunction(Game.base.add(0xefa38), 'void', ['pointer', 'int'])

        //     Interceptor.replace(
        //         targetAddress,
        //         new NativeCallback(
        //             function (arg1, arg2) {
        //                 console.log('Input called: ', arg2)

        //                 if (getCache('input_last_key') == arg2) {
        //                     console.log('Last key pressed is the same as the current one, skipping')
        //                     return
        //                 }

        //                 putCache('input_last_key', arg2, 100)
        //                 targetAddress(arg1, arg2)
        //             },
        //             'void',
        //             ['pointer', 'int']
        //         )
        //     ) // Assuming the method doesn't return anything, adjust accordingly

        //     console.log('Method sub_efa38 replaced and blocked')
        // })
    }

    static initData() {
        let start = Time.now()

        WebData.init()
        log('Loaded web data within', Time.now() - start + 'ms')

        start = Time.now()
        ObjectDefinitions.init()
        log('Loaded object definitions within', Time.now() - start + 'ms')

        start = Time.now()
        ItemDefinitions.init()
        log('Loaded item definitions within', Time.now() - start + 'ms')

        start = Time.now()
        NpcDefinitions.init()
        log('Loaded npc definitions within', Time.now() - start + 'ms')

        start = Time.now()
        VarbitDefinitions.init()
        log('Loaded varbit definitions within', Time.now() - start + 'ms')

        start = Time.now()
        Names.init()
        log('Loaded names within', Time.now() - start + 'ms')
    }

    static initBackgroundTasks() {
        let start = Time.now()

        ScriptStarter.start()

        log('OnlineDataSender')
        OnlineDataSender.start()
        log('Took ', Time.now() - start)

        log('CrashMonitor')
        CrashMonitor.start()
        log('Took ', Time.now() - start)

        log('GameStateMonitor')
        GameStateMonitor.start()
        log('Took ', Time.now() - start)

        MessedAccountGuard.start()

        log('SessionLimiter')
        SessionLimiter.start()
        log('Took ', Time.now() - start)

        log('Starting ScriptActiveTimeMonitor')
        ScriptActiveTimeMonitor.start()

        IpAddressChecker.start()

        
        if (!BotSettings.devMode) {
            log('Starting LowCpuSetter')
            LowCpuSetter.start()
        }
    }

    static widgetStructAlloc: NativePointer

    static initBasicEventListeners() { }

    static initHealthBars() {
        Interceptor.attach(Game.base.add(Offsets.Game.addHealthBar), {
            onEnter: (args) => {
                putCache('healthbar_' + args[0], args[5].toInt32(), 10000)
            },
            onLeave: () => { },
        })
    }

    static initCollisionCaching() {
        //2 Of these methods because they do & and |

        
        Interceptor.attach(Game.base.add(Offsets.Game.onCollisionFlagsOr), {
            onEnter: (args) => {
                const x = args[1].toInt32()
                const y = args[2].toInt32()
                this.x = x
                this.y = y
            },
            onLeave: () => {
                CachedCollisionFlagsContainer.updateCache(this.x, this.y, Client.plane)
            },
        })

        Interceptor.attach(Game.base.add(Offsets.Game.onColliosnFlagsAnd), {
            onEnter: (args) => {
                const x = args[1].toInt32()
                const y = args[2].toInt32()
                this.x = x
                this.y = y
            },
            onLeave: () => {
                CachedCollisionFlagsContainer.updateCache(this.x, this.y, Client.plane)
            },
        })
    }

    static initLowCpu() {
        const originalFunction = new NativeFunction(Game.base.add(Offsets.Game.renderTiles), 'uint64', ['uint64', 'uint64', 'int', 'int', 'int', 'int', 'int', 'int', 'int'])
        Interceptor.replace(
            originalFunction,
            new NativeCallback(
                function (arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9) {
                    if (LowCpu.isEnabled) {
                        return
                    }

                    return originalFunction(arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9)
                },
                'void',
                ['uint64', 'uint64', 'int', 'int', 'int', 'int', 'int', 'int', 'int']
            )
        )
    }

    static initSoundEffects() {
        Interceptor.attach(Game.base.add(Offsets.Game.onSoundEffect), {
            onEnter: (args) => {
                // runOnUtilityThread(() => {
                const soundId = args[1].toInt32();
                log('[Sound Effect] ', soundId, args[2].toInt32(), args[3].toInt32())

                this.eventBus.invoke(SoundEffectEvent, {
                    id: soundId
                })
                // })
            },
            onLeave: () => { },
        })
    }

    static initTest() {

        // const getSceneTileAtFunc = new NativeFunction(Game.base.add("0x479970"), "pointer", ["pointer", "int", "int", "int"])

        // const sceneTile = getSceneTileAtFunc(Client.get(), localTile.x, localTile.y, 0)

        // Interceptor.attach(Game.base.add(0x479970), {
        //     onEnter: (args) => {
        //         log('Some call test: ', args[0], args[1].toInt32(), args[2].toInt32(), args[3].toInt32())
        //     },
        //     onLeave: () => {},
        // })       
        // 
        // // Interceptor.attach(Game.base.add(0x218100), {
        //     onEnter: (args) => {
        //         //arg[] is just client pointer
        //         log('Sound effect: ', args[0], args[1].toInt32(), args[2].toInt32(), args[3].toInt32())
        //     },
        //     onLeave: () => {},
        // })

        // Interceptor.attach(Game.base.add(0x222620), {
        //     onEnter: (args) => {
        //         log("Render scene")
        //         return 0
        //     },
        //     onLeave: (retval) => {
        //         log("return value: ", retval)
        //     }
        // })

        // const originalFunction = new NativeFunction(ptr('0xYOUR_ADDRESS'), 'void', []);
        // Interceptor.replace(ptr('0xYOUR_ADDRESS'), new NativeCallback(function() {
        //     if (someCondition) {
        //         console.log('Stopping execution');
        //         return;
        //     }

        //     // Call original with all arguments using 'this.context'
        //     return originalFunction.apply(null, Array.prototype.slice.call(arguments));
        // }, 'void', []));

        // const func = new NativeFunction(Game.base.add(0x222620), 'int64', ['uint64', 'int32', 'uint32', 'int32', 'int32', 'int32'])
        // Interceptor.replace(
        //     func,
        //     //@ts-ignore
        //     new NativeCallback((arg1, arg2, arg3, arg4, arg5, arg6) => {
        //             if(LowCpu.isEnabled) {

        //             }
        //             const val =  func(arg1, arg2, arg3, arg4, arg5, arg6)
        //             log("Val of func: ", val)
        //             return val
        //         },
        //         'int64',
        //         ['uint64', 'int32', 'uint32', 'int32', 'int32', 'int32']
        //     )
        // )

        // Interceptor.attach(Game.base.add(0x3757d0), {
        //     onEnter: (args) => {
        //             log("Player add", args[0], args[1], args[2], args[3], args[4], args[5])
        //             lastPrint = Date.now()
        //     },
        //     onLeave: () => {}
        // })

        // Interceptor.attach(Game.base.add(0x256550), {
        //     onEnter: (args) => {
        //             log("Update entity", args[0], args[1], args[2])
        //             lastPrint = Date.now()
        //     },
        //     onLeave: () => {}
        // })

        //sub_15f5f0(int128_t* arg1, void* arg2, int32_t arg3, int32_t arg4, int32_t arg5, int32_t* arg6)
        // Interceptor.attach(Game.base.add(0x4f9050), {
        //     onEnter: (args) => {
        //         log('jagex auth req', args[0], args[1])
        //     },
        //     onLeave: () => {},
        // })
        // Interceptor.attach(Game.base.add(0x8ba25a), {
        //     onEnter: (args) => {
        //         log('Something with ssl')
        //     },
        //     onLeave: () => {},
        // })
        // Interceptor.attach(Game.base.add(0x002b8650), {
        //     onEnter: (args) => {
        //         // log('check login state', args[0]) //
        //     },
        //     onLeave: () => {},
        // })
        // Interceptor.attach(Game.base.add(0x2b6570), {
        //     onEnter: (args) => {
        //         log('Write token there?') ///
        //     },
        //     onLeave: () => {},
        // })
        // Interceptor.attach(Game.base.add(0x2b9dd0), {
        //     onEnter: (args) => {
        //         const calledMethod = readJagString(args[1])
        //         log('jagex auth req', args[0], readJagString(args[1]), readJagString(args[2]))
        //         if (calledMethod == 'authenticateWithJagexWeb') {
        //             log('Stopping from jagex sign in and manually setting auth data')
        //         }
        //     },
        //     onLeave: () => {},
        // })
        //@prettier-ignore
        // const fun = new NativeFunction(Game.base.add(Offsets.Game.getTileHeight), 'int', ['pointer', 'int', 'int', 'int'])
        // Interceptor.replace(
        //     fun,
        //     new NativeCallback(
        //         (arg1, arg2, arg3, arg4) => {
        //             const h = fun(arg1, arg2, arg3, arg4)
        //             log('Asked for height: ', arg1, arg2, arg3, arg4, ' = ', h)
        //             return h
        //         },
        //         'int',
        //         ['pointer', 'int', 'int', 'int']
        //     )
        // )
    }

  static initMenuInvokeInterceptor() {
        log('Injecting menu invoke interceptor ')

        Interceptor.replace(
            Menu.invokeMenuActionHook,
            new NativeCallback(
                (clientPointer, iArg1, iArg2, iArg3, iArg4, iArg5, sArg6, sArg7, iArg8, iArg9, widgetArg10) => {
                    if (Bot.scriptHandler.currentlyExecuting != null && iArg9 >= 0) {
                        if (Debuggers.isGameDebug) {
                            // log(`[MenuAction BLOCKED]`, arg1, arg2, arg3, arg4 /*, "", itemIdArg6.add(0x34).readInt(), arg7,arg8,widgetArg9 + " = " + widgetArg9.readPointer()*/)
                        }
                        return -1
                    }

                    iArg8 = -iArg8
                    iArg9 = -iArg9

                    const action = readJagString(sArg6)
                    log(
                        `[MenuAction]`,
                        iArg1,
                        iArg2,
                        iArg3,
                        iArg4,
                        iArg5,
                        action,
                        readJagString(sArg7),
                        '(itemId:' + sArg7.add(0x34).readInt() + ')',
                        iArg8,
                        iArg9,
                        widgetArg10 + ' = ' + widgetArg10.readPointer()
                    )

                    return Menu.invokeMenuActionHook(clientPointer, iArg1, iArg2, iArg3, iArg4, iArg5, sArg6, sArg7, iArg8, iArg9, widgetArg10)
                },
                'int64',
                ['pointer', 'int', 'int', 'int', 'int', 'int', 'pointer', 'pointer', 'int', 'int', 'pointer']
            )
        )
    }

    static initClientTick() {
        let serverTickBefore = 0
        Interceptor.attach(Game.base.add(Offsets.Game.onClientTick), {
            onEnter: (args) => {
                this.lastGameCycle = Date.now()

                try {
                    for (const fn of this.functionsToInvokeOnGameThread) {
                        fn()
                    }
                } catch { }

                this.functionsToInvokeOnGameThread = []

                if (LowCpu.isEnabled) {
                    javaPerformNow(() => Time.sleep(LowCpu.renderDelay))
                }

                const currentTick = Client.serverTick

                this.eventBus.invoke(ClientTickEvent, {})

                if (currentTick != serverTickBefore) {
                    this.eventBus.invoke(ServerTickEvent, {
                        currentTick: currentTick,
                        previousTick: serverTickBefore,
                    })

                    serverTickBefore = Client.serverTick
                }
            },
            onLeave: () => { },
        })
    }

    static invokeOnGameThread(fn: Function) {
        this.functionsToInvokeOnGameThread.push(fn)
    }

    static initOnHardwareDataRequested() {
        //There is method in code, that might request variables like USER_DISPLAY_NAME, STATE_SESSION_DURATION, STATE_LAST_MEMORY_TRIM and more
        //which could be used for tracking hardware data. Currently it was not used, but just in case if these data is requested in future, we will be
        //killing the game process.
        const targetAddress = Game.base.add(Offsets.Game.onHardwareDataRequested)
        Interceptor.replace(
            targetAddress,
            new NativeCallback(
                (arg0, arg1) => {
                    // Read the hardware data request parameter (assumed to be a jag string)
                    const requestedData = readJagString(arg1)
                    log('Server requested some hardware data: ' + requestedData)
                    toast('Server requested hardware data: ' + requestedData)

                    Debuggers.hardwareDataRequests++

                    // Optionally, you can add actions here like killCurrentProcess if needed
                    // For now, we simply return a "dummy" result to satisfy the caller
                    return 0
                },
                'int',
                ['pointer', 'pointer']
            )
        )
    }


    static initBackButtonPrevent() {
        javaPerform(function () {
            var activityClassName = "com.jagex.android.MainActivity"; 
            var Activity = Java.use(activityClassName);

            Activity.onBackPressed.implementation = function () {
                log("[Frida] onBackPressed() called! Blocking execution.");
                logSentry("Back button pressed")
            };

            Activity.onUserLeaveHint.implementation = function () {
                log("[Frida] onUserLeaveHint() called! User likely pressed Home or Recents. Calling original.");
                logSentry("onUserLeaveHint")
            };

        });
    }

    static initOnMessageListener() {
        Interceptor.attach(Game.base.add(Offsets.Game.onMessageAdded), {
            onEnter: (args) => {
                const username = readJagString(args[2])
                const message = readJagString(args[4])

                if (Debuggers.isGameDebug) {
                    log('[OnMessage]', username, message)
                }

                this.eventBus.invoke(GameMessageEvent, {
                    message: message,
                    username: username,
                })

                Bot.scriptHandler.currentlyExecuting?.onGameMessage(username, message)
                GrandExchange.onGameMessage(message)

            },
            onLeave: () => { },
        })
    }

    static cachedWidgets: any = []

    static initDrawWidgets() {
        Interceptor.attach(Game.base.add(Offsets.Game.onRenderWidgets), {
            onEnter: (args) => {
                const parentId = args[4].toInt32()
                const x = args[6].toInt32()
                const y = args[7].toInt32()

                const cached = getCache('widgetPosition_' + parentId)
                if (cached?.x == x && cached?.y == y) {
                    return
                }

                if (parentId == -1 || parentId < 100 || parentId == 65535) {
                    return
                }

                putCache('widgetPosition_' + parentId, { x: x, y: y })
            },
            onLeave: () => { },
        })
    }
}
