import { Varps } from './varps'

export class Quest {
    //static FORGETTABLE_TALE = Quest.varbit('FORGETTABLE_TALE', 822, -1)
    //static MOURNINGS_END_PART_II = Quest.varbit('MOURNINGS_END_PART_II', 1103, -1)
    //static THRONE_OF_MISCELLANIA = Quest.varPlayer('THRONE_OF_MISCELLANIA', 359, -1)
    static TROLL_STRONGHOLD = Quest.varPlayer('TROLL_STRONGHOLD', 317, -1)
    static EADGARS_RUSE = Quest.varPlayer('EADGARS_RUSE', 335, -1)
    //static CURSE_OF_THE_EMPTY_LORD = Quest.varbit('CURSE_OF_THE_EMPTY_LORD', 821, -1)
    //static SHEEP_SHEARER = Quest.varPlayer('SHEEP_SHEARER', 179, -1)
    //static DEATH_TO_THE_DORGESHUUN = Quest.varbit('DEATH_TO_THE_DORGESHUUN', 2258, -1)
    //static ELEMENTAL_WORKSHOP_I = Quest.varPlayer('ELEMENTAL_WORKSHOP_I', 299, -1)
    //static GHOSTS_AHOY = Quest.varbit('GHOSTS_AHOY', 217, -1)
    //static THE_GIANT_DWARF = Quest.varbit('THE_GIANT_DWARF', 571, -1)
    //static SCORPION_CATCHER = Quest.varPlayer('SCORPION_CATCHER', 76, -1)
    //static ROYAL_TROUBLE = Quest.varbit('ROYAL_TROUBLE', 2140, -1)
    //static RECIPE_FOR_DISASTER_SIR_AMIK_VARZE = Quest.varbit('RECIPE_FOR_DISASTER_SIR_AMIK_VARZE', 1910, -1)
    //static RAG_AND_BONE_MAN_II = Quest.varPlayer('RAG_AND_BONE_MAN_II', 714, -1)
    //static MONKEY_MADNESS_II = Quest.varbit('MONKEY_MADNESS_II', 5027, -1)

    //static LEGENDS_QUEST = Quest.varPlayer('LEGENDS_QUEST', 139, -1)
    //static HEROES_QUEST = Quest.varPlayer('HEROES_QUEST', 188, -1)
    //static THE_GREAT_BRAIN_ROBBERY = Quest.varPlayer('THE_GREAT_BRAIN_ROBBERY', 980, -1)

    //Minigames
    //Kudos after found all cleaning:
    /*15:21:20: Config 262: 4 -> 272
        15:21:18: Varbit 15064: 0 -> 100
        15:21:18: Config 3939: 0 -> 100
        15:21:11: Varbit 8354: 860 -> 861
        15:21:11: Config 1042: 27520 -> 27552
        15:21:11: Config 1021: 0 -> 512
        15:21:10: Varbit 3639: 0 -> 1
        15:21:10: Config 1010: 67649 -> 71745
        15:21:09: Config 1021: 512 -> 0*/

    /*
        OR JUST SETTING >= 75

        VARBITS
        pottery
        old symbol 3646
        ancien symbol 3645
        ancien coin 3647
        old coin 3648
        necklace 3639
    */
    static CLEAN_MUSEUM_FINDS = Quest.varbits('CLEAN_MUSEUM_FINDS', [
        { varbit: 3643, completedValue: 1 },
        { varbit: 3644, completedValue: 1 },
        { varbit: 3645, completedValue: 1 },
        { varbit: 3646, completedValue: 1 },
        { varbit: 3647, completedValue: 1 },
        { varbit: 3648, completedValue: 1 },
        { varbit: 3639, completedValue: 1 },
    ])

        static ALFRED_GRIMHANDS_BARCRAWL = Quest.varPlayer('ALFRED_GRIMHANDS_BARCRAWL', 77, 2, 'equal')
    static LUNAR_DIPLOMACY = Quest.varbit('LUNAR_DIPLOMACY', 2448, 190)

    static VARROCK_MUESEUM = Quest.varbit('VARROCK_MUESEUM', 3688, 1)

    static BONE_VOYAGE = Quest.varbit('BONE_VOYAGE', 5795, 50)

    static THE_FREMENNIK_ISLES = Quest.varbit('THE_FREMENNIK_ISLES', 3311, 340)
    static MURDER_MYSTERY = Quest.varPlayer('MURDER_MYSTERY', 192, 2) //TODO check arg

    static THE_FREMENNIK_TRIALS = Quest.varPlayer('THE_FREMENNIK_TRIALS', 347, 8)
    static MERLINS_CRYSTAL = Quest.varPlayer('MERLINS_CRYSTAL', 14, 6)
    static THE_GRAND_TREE = Quest.varPlayer('THE_GRAND_TREE', 150, 150)
    static BELOW_ICE_MOUNTAIN = Quest.varbit('BELOW_ICE_MOUNTAIN', 12063, 40)
    static BLACK_KNIGHTS_FORTRESS = Quest.varPlayer('BLACK_KNIGHTS_FORTRESS', 130, 3)
    static COOKS_ASSISTANT = Quest.varPlayer('COOKS_ASSISTANT', 29, 1)
    static THE_CORSAIR_CURSE = Quest.varbit('THE_CORSAIR_CURSE', 6071, 55)
    static DEMON_SLAYER = Quest.varbit('DEMON_SLAYER', 2561, 2)
    static DORICS_QUEST = Quest.varPlayer('DORICS_QUEST', 31, 10)
    static DRAGON_SLAYER_I = Quest.varPlayer('DRAGON_SLAYER', 176, 9)
    static ERNEST_THE_CHICKEN = Quest.varPlayer('ERNEST_THE_CHICKEN', 32, 2)
    static GOBLIN_DIPLOMACY = Quest.varbit('GOBLIN_DIPLOMACY', 2378, 5)
    static IMP_CATCHER = Quest.varPlayer('IMP_CATCHER', 160, 1)
    static THE_KNIGHTS_SWORD = Quest.varPlayer('THE_KNIGHTS_SWORD', 122, 6)
    static MISTHALIN_MYSTERY = Quest.varbit('MISTHALIN_MYSTERY', 3468, 130)
    static PIRATES_TREASURE = Quest.varPlayer('PIRATES_TREASURE', 71, 3)
    static PRINCE_ALI_RESCUE = Quest.varPlayer('PRINCE_ALI_RESCUE', 273, 100)
    static THE_RESTLESS_GHOST = Quest.varPlayer('THE_RESTLESS_GHOST', 107, 4)
    static ROMEO__JULIET = Quest.varPlayer('ROMEO__JULIET', 144, 60)
    static RUNE_MYSTERIES = Quest.varPlayer('RUNE_MYSTERIES', 63, 5)
    static SHIELD_OF_ARRAV_PHOENIX_GANG = Quest.varPlayer('SHIELD_OF_ARRAV_PHOENIX_GANG', 145, 9)
    static SHIELD_OF_ARRAV_BLACK_ARM_GANG = Quest.varPlayer('SHIELD_OF_ARRAV_BLACK_ARM_GANG', 146, 3)
    static VAMPYRE_SLAYER = Quest.varPlayer('VAMPIRE_SLAYER', 178, 2)
    static WITCHS_POTION = Quest.varPlayer('WITCHS_POTION', 67, 2)
    static X_MARKS_THE_SPOT = Quest.varbit('X_MARKS_THE_SPOT', 8063, 7)
    static ANIMAL_MAGNETISM = Quest.varbit('ANIMAL_MAGNETISM', 3185, 230)
    static ANOTHER_SLICE_OF_HAM = Quest.varbit('ANOTHER_SLICE_OF_HAM', 3550, 10)
    static BENEATH_CURSED_SANDS = Quest.varbit('BENEATH_CURSED_SANDS', 13841, 106)
    static BETWEEN_A_ROCK = Quest.varbit('BETWEEN_A_ROCK', 299, 100)
    static BIG_CHOMPY_BIRD_HUNTING = Quest.varPlayer('BIG_CHOMPY_BIRD_HUNTING', 293, 60)
    static BIOHAZARD = Quest.varPlayer('BIOHAZARD', 68, 15)
    static CABIN_FEVER = Quest.varPlayer('CABIN_FEVER', 655, 130)
    static CLOCK_TOWER = Quest.varPlayer('CLOCK_TOWER', 10, 7)
    static COLD_WAR = Quest.varbit('COLD_WAR', 3293, 130)
    static CONTACT = Quest.varbit('CONTACT', 3274, 120)
    static CREATURE_OF_FENKENSTRAIN = Quest.varPlayer('CREATURE_OF_FENKENSTRAIN', 399, 6)
    static DARKNESS_OF_HALLOWVALE = Quest.varbit('DARKNESS_OF_HALLOWVALE', 2573, 310)
    static DEATH_PLATEAU = Quest.varPlayer('DEATH_PLATEAU', 314, 70)
    static THE_DEPTHS_OF_DESPAIR = Quest.varbit('THE_DEPTHS_OF_DESPAIR', 6027, 10)
    static DESERT_TREASURE = Quest.varbit('DESERT_TREASURE', 358, 14)
    static DESERT_TREASURE_II = Quest.varbit('DESERT_TREASURE_II', 14862, 114)
    static DEVIOUS_MINDS = Quest.varbit('DEVIOUS_MINDS', 1465, 70)
    static THE_DIG_SITE = Quest.varPlayer('THE_DIG_SITE', 131, 8)
    static DRAGON_SLAYER_II = Quest.varbit('DRAGON_SLAYER_II', 6104, 210)
    static DREAM_MENTOR = Quest.varbit('DREAM_MENTOR', 3618, 26)
    static DRUIDIC_RITUAL = Quest.varPlayer('DRUIDIC_RITUAL', 80, 3)
    static DWARF_CANNON = Quest.varPlayer('DWARF_CANNON', 0, 10)
    static EAGLES_PEAK = Quest.varbit('EAGLES_PEAK', 2780, 35)
    static ELEMENTAL_WORKSHOP_II = Quest.varbit('ELEMENTAL_WORKSHOP_II', 2639, 10)
    static ENAKHRAS_LAMENT = Quest.varbit('ENAKHRAS_LAMENT', 1560, 60)
    static ENLIGHTENED_JOURNEY = Quest.varbit('ENLIGHTENED_JOURNEY', 2866, 100)
    static THE_EYES_OF_GLOUPHRIE = Quest.varbit('THE_EYES_OF_GLOUPHRIE', 2497, 50)
    static THE_PATH_OF_GLOUPHRIE = Quest.varbit('THE_PATH_OF_GLOUPHRIE', 15288, 48)
    static FAIRYTALE_I__GROWING_PAINS = Quest.varbit('FAIRYTALE_I__GROWING_PAINS', 1803, 80)
    static FAIRYTALE_II__CURE_A_QUEEN = Quest.varbit('FAIRYTALE_II__CURE_A_QUEEN', 2326, 80)
    static FAMILY_CREST = Quest.varPlayer('FAMILY_CREST', 148, 10)
    static THE_FEUD = Quest.varbit('THE_FEUD', 334, 27)
    static FIGHT_ARENA = Quest.varPlayer('FIGHT_ARENA', 17, 14)
    static FISHING_CONTEST = Quest.varPlayer('FISHING_CONTEST', 11, 4)

    static GARDEN_OF_TRANQUILLITY = Quest.varbit('GARDEN_OF_TRANQUILLITY', 961, 50)
    static GERTRUDES_CAT = Quest.varPlayer('GERTRUDES_CAT', 180, 5)
    static THE_GOLEM = Quest.varbit('THE_GOLEM', 346, 7)

    static GRIM_TALES = Quest.varbit('GRIM_TALES', 2783, 50)
    static THE_HAND_IN_THE_SAND = Quest.varbit('THE_HAND_IN_THE_SAND', 1527, 150)
    static HAUNTED_MINE = Quest.varPlayer('HAUNTED_MINE', 382, 10)
    static HAZEEL_CULT = Quest.varPlayer('HAZEEL_CULT', 223, 7)

    static HOLY_GRAIL = Quest.varPlayer('HOLY_GRAIL', 5, 9)
    static HORROR_FROM_THE_DEEP = Quest.varbit('HORROR_FROM_THE_DEEP', 34, 5)
    static ICTHLARINS_LITTLE_HELPER = Quest.varbit('ICTHLARINS_LITTLE_HELPER', 418, 25)
    static IN_AID_OF_THE_MYREQUE = Quest.varbit('IN_AID_OF_THE_MYREQUE', 1990, 420)
    static IN_SEARCH_OF_THE_MYREQUE = Quest.varPlayer('IN_SEARCH_OF_THE_MYREQUE', 387, 105)
    static JUNGLE_POTION = Quest.varPlayer('JUNGLE_POTION', 175, 11)
    static KINGS_RANSOM = Quest.varbit('KINGS_RANSOM', 3888, 85)
    static LAND_OF_THE_GOBLINS = Quest.varbit('LAND_OF_THE_GOBLINS', 13599, 52)

    static LOST_CITY = Quest.varPlayer('LOST_CITY', 147, 5)
    static THE_LOST_TRIBE = Quest.varbit('THE_LOST_TRIBE', 532, 10)

    static MAKING_FRIENDS_WITH_MY_ARM = Quest.varbit('MAKING_FRIENDS_WITH_MY_ARM', 6528, 196)
    static MAKING_HISTORY = Quest.varbit('MAKING_HISTORY', 1383, 3)

    static MONKEY_MADNESS_I = Quest.varPlayer('MONKEY_MADNESS', 365, 7)

    static MONKS_FRIEND = Quest.varPlayer('MONKS_FRIEND', 30, 70)
    static MOUNTAIN_DAUGHTER = Quest.varbit('MOUNTAIN_DAUGHTER', 260, 60)
    static MOURNINGS_END_PART_I = Quest.varPlayer('MOURNINGS_END_PART_I', 517, 8)

    static MY_ARMS_BIG_ADVENTURE = Quest.varbit('MY_ARMS_BIG_ADVENTURE', 2790, 310)
    static NATURE_SPIRIT = Quest.varPlayer('NATURE_SPIRIT', 307, 105)
    static OBSERVATORY_QUEST = Quest.varPlayer('OBSERVATORY_QUEST', 112, 6)
    static OLAFS_QUEST = Quest.varbit('OLAFS_QUEST', 3534, 70)
    static ONE_SMALL_FAVOUR = Quest.varPlayer('ONE_SMALL_FAVOUR', 416, 275)
    static PLAGUE_CITY = Quest.varPlayer('PLAGUE_CITY', 165, 28)
    static PRIEST_IN_PERIL = Quest.varPlayer('PRIEST_IN_PERIL', 302, 59)
    static THE_QUEEN_OF_THIEVES = Quest.varbit('THE_QUEEN_OF_THIEVES', 6037, 12)
    static RAG_AND_BONE_MAN_I = Quest.varPlayer('RAG_AND_BONE_MAN_I', 714, 3)

    static RATCATCHERS = Quest.varbit('RATCATCHERS', 1404, 125)
    static RECIPE_FOR_DISASTER_START = Quest.varbit('RECIPE_FOR_DISASTER_START', 1850, 2)
    static RECIPE_FOR_DISASTER_DWARF = Quest.varbit('RECIPE_FOR_DISASTER_DWARF', 1892, 60)
    static RECIPE_FOR_DISASTER_WARTFACE_AND_BENTNOZE = Quest.varbit('RECIPE_FOR_DISASTER_WARTFACE_AND_BENTNOZE', 1867, 40)
    static RECIPE_FOR_DISASTER_PIRATE_PETE = Quest.varbit('RECIPE_FOR_DISASTER_PIRATE_PETE', 1895, 110)
    static RECIPE_FOR_DISASTER_LUMBRIDGE_GUIDE = Quest.varbit('RECIPE_FOR_DISASTER_LUMBRIDGE_GUIDE', 1896, 4)
    static RECIPE_FOR_DISASTER_EVIL_DAVE = Quest.varbit('RECIPE_FOR_DISASTER_EVIL_DAVE', 1878, 5)
    static RECIPE_FOR_DISASTER_MONKEY_AMBASSADOR = Quest.varbit('RECIPE_FOR_DISASTER_MONKEY_AMBASSADOR', 1914, 40)

    static RECIPE_FOR_DISASTER_SKRACH_UGLOGWEE = Quest.varbit('RECIPE_FOR_DISASTER_SKRACH_UGLOGWEE', 1904, 170)
    static RECIPE_FOR_DISASTER_FINALE = Quest.varbit('RECIPE_FOR_DISASTER_FINALE', 1850, 4)
    static RECRUITMENT_DRIVE = Quest.varbit('RECRUITMENT_DRIVE', 657, 1)
    static REGICIDE = Quest.varPlayer('REGICIDE', 328, 14)
    static ROVING_ELVES = Quest.varPlayer('ROVING_ELVES', 402, 5)

    static RUM_DEAL = Quest.varPlayer('RUM_DEAL', 600, 18)

    static SEA_SLUG = Quest.varPlayer('SEA_SLUG', 159, 11)
    static SHADES_OF_MORTTON = Quest.varPlayer('SHADES_OF_MORTTON', 339, 80)
    static SHADOW_OF_THE_STORM = Quest.varbit('SHADOW_OF_THE_STORM', 1372, 124)
    static SHEEP_HERDER = Quest.varPlayer('SHEEP_HERDER', 60, 2)
    static SHILO_VILLAGE = Quest.varPlayer('SHILO_VILLAGE', 116, 14)
    static SLEEPING_GIANTS = Quest.varbit('SLEEPING_GIANTS', 13902, 25)
    static THE_SLUG_MENACE = Quest.varbit('THE_SLUG_MENACE', 2610, 12)
    static A_SOULS_BANE = Quest.varbit('A_SOULS_BANE', 2011, 12)
    static SPIRITS_OF_THE_ELID = Quest.varbit('SPIRITS_OF_THE_ELID', 1444, 55)
    static SWAN_SONG = Quest.varbit('SWAN_SONG', 2098, 190)
    static TAI_BWO_WANNAI_TRIO = Quest.varPlayer('TAI_BWO_WANNAI_TRIO', 320, 5)
    static A_TAIL_OF_TWO_CATS = Quest.varbit('A_TAIL_OF_TWO_CATS', 1028, 65)
    static TALE_OF_THE_RIGHTEOUS = Quest.varbit('TALE_OF_THE_RIGHTEOUS', 6358, 16)
    static A_TASTE_OF_HOPE = Quest.varbit('A_TASTE_OF_HOPE', 6396, 160)
    static TEARS_OF_GUTHIX = Quest.varbit('TEARS_OF_GUTHIX', 451, 1)
    static TEMPLE_OF_IKOV = Quest.varPlayer('TEMPLE_OF_IKOV', 26, 70)
    static TEMPLE_OF_THE_EYE = Quest.varbit('TEMPLE_OF_THE_EYE', 13738, 125)

    static THE_TOURIST_TRAP = Quest.varPlayer('THE_TOURIST_TRAP', 197, 29)
    static TOWER_OF_LIFE = Quest.varbit('TOWER_OF_LIFE', 3337, 17)
    static TREE_GNOME_VILLAGE = Quest.varPlayer('TREE_GNOME_VILLAGE', 111, 8)
    static TRIBAL_TOTEM = Quest.varPlayer('TRIBAL_TOTEM', 200, 4)
    static TROLL_ROMANCE = Quest.varPlayer('TROLL_ROMANCE', 385, 40)

    static UNDERGROUND_PASS = Quest.varPlayer('UNDERGROUND_PASS', 161, 10)
    static CLIENT_OF_KOUREND = Quest.varbit('CLIENT_OF_KOUREND', 5619, 6)
    static WANTED = Quest.varbit('WANTED', 1051, 10)
    static WATCHTOWER = Quest.varPlayer('WATCHTOWER', 212, 12)
    static WATERFALL_QUEST = Quest.varPlayer('WATERFALL_QUEST', 65, 8)
    static WHAT_LIES_BELOW = Quest.varbit('WHAT_LIES_BELOW', 3523, 140)
    static WITCHS_HOUSE = Quest.varPlayer('WITCHS_HOUSE', 226, 6)
    static ZOGRE_FLESH_EATERS = Quest.varbit('ZOGRE_FLESH_EATERS', 487, 12)
    static THE_ASCENT_OF_ARCEUUS = Quest.varbit('THE_ASCENT_OF_ARCEUUS', 7856, 13)
    static THE_FORSAKEN_TOWER = Quest.varbit('THE_FORSAKEN_TOWER', 7796, 10)
    static SONG_OF_THE_ELVES = Quest.varbit('SONG_OF_THE_ELVES', 9016, 192)
    static THE_FREMENNIK_EXILES = Quest.varbit('THE_FREMENNIK_EXILES', 9459, 125)
    static SINS_OF_THE_FATHER = Quest.varbit('SINS_OF_THE_FATHER', 7255, 136)
    static GETTING_AHEAD = Quest.varbit('GETTING_AHEAD', 693, 32)
    static A_PORCINE_OF_INTEREST = Quest.varbit('A_PORCINE_OF_INTEREST', 10582, 35)
    static A_KINGDOM_DIVIDED = Quest.varbit('A_KINGDOM_DIVIDED', 12296, 150)
    static A_NIGHT_AT_THE_THEATRE = Quest.varbit('A_NIGHT_AT_THE_THEATRE', 12276, 86)
    static THE_GARDEN_OF_DEATH = Quest.varbit('THE_GARDEN_OF_DEATH', 14609, 54)
    static SECRETS_OF_THE_NORTH = Quest.varbit('SECRETS_OF_THE_NORTH', 14722, 88)
    static CHILDREN_OF_THE_SUN = Quest.varbit('CHILDREN_OF_THE_SUN', 9632, 22)
    static DEFENDER_OF_VARROCK = Quest.varbit('DEFENDER_OF_VARROCK', 9655, 54)
    static AT_FIRST_LIGHT = Quest.varbit('AT_FIRST_LIGHT', 9835, 11)
    static PERILOUS_MOON = Quest.varbit('PERILOUS_MOON', 9819, 31)
    static THE_RIBBITING_TALE_OF_A_LILY_PAD_LABOUR_DISPUTE = Quest.varbit('THE_RIBBITING_TALE_OF_A_LILY_PAD_LABOUR_DISPUTE', 9844, 30)
    static TWILIGHTS_PROMISE = Quest.varbit('TWILIGHTS_PROMISE', 9649, 48)
    static ENTER_THE_ABYSS = Quest.varPlayer('ENTER_THE_ABYSS', 492, 3)
    static BEAR_YOUR_SOUL = Quest.varbit('BEAR_YOUR_SOUL', 5078, 2)

    static ENCHANTED_KEY = Quest.varbit('ENCHANTED_KEY', 1391, 2046)
    static THE_GENERALS_SHADOW = Quest.varbit('THE_GENERALS_SHADOW', 3330, 25)
    static SKIPPY_AND_THE_MOGRES = Quest.varbit('SKIPPY_AND_THE_MOGRES', 1344, 2)
    static THE_MAGE_ARENA = Quest.varPlayer('THE_MAGE_ARENA', 267, 7)
    static LAIR_OF_TARN_RAZORLOR = Quest.varbit('LAIR_OF_TARN_RAZORLOR', 3290, 2)
    static FAMILY_PEST = Quest.varbit('FAMILY_PEST', 5347, 2)
    static THE_MAGE_ARENA_II = Quest.varbit('THE_MAGE_ARENA_II', 6067, 3)
    static IN_SEARCH_OF_KNOWLEDGE = Quest.varbit('IN_SEARCH_OF_KNOWLEDGE', 8403, 2)
    static DADDYS_HOME = Quest.varbit('DADDYS_HOME', 10570, 12)
    static HOPESPEARS_WILL = Quest.varbit('HOPESPEARS_WILL', 13619, 1)
    static HIS_FAITHFUL_SERVANTS = Quest.varbit('HIS_FAITHFUL_SERVANTS', 14973, 8)
    static KNIGHT_WAVES_TRAINING_GROUNDS = Quest.varbit('KNIGHT_WAVES_TRAINING_GROUNDS', 3909, 8)

    static KARAMJA_MEDIUM = Quest.varbit('KARAMJA_MEDIUM', 3598, 1)
    static KARAMJA_EASY = Quest.varbit('KARAMJA_EASY', 3577, 1)

    static STRONGHOLD_OF_SECURITY = Quest.varbit('STRONGHOLD_OF_SECURITY', 2312, 0)

    varbit?: number
    varbits?: { varbit: number; completedValue: number }[]

    varplayer?: number
    dreambotName: string
    lastStepValue: number
    comparisonMode?: 'equalOrGreaterThan' | 'equal' = 'equalOrGreaterThan'

    constructor() {}

    get isCompleted() {
        if (this.varbits != null) {
            for (const varbit of this.varbits) {
                if (Varps.getVarbit(varbit.varbit) < varbit.completedValue) {
                    return false
                }
            }
            return true
        }

        if (this.comparisonMode === 'equal') {
            return this.step === this.lastStepValue
        }

        // Default is 'equalOrGreaterThan'
        return this.step >= this.lastStepValue
    }

    get step(): number {
        if (this.varbits != null) {
            for (const varbit of this.varbits) {
                if (Varps.getVarbit(varbit.varbit) === varbit.completedValue) {
                    return Varps.getVarbit(varbit.varbit)
                }
            }
            return -1
        }
        if (this.varbit != null) {
            return Varps.getVarbit(this.varbit)
        }
        if (this.varplayer != null) {
            return Varps.getConfig(this.varplayer)
        }
        return -1
    }

    static varbits(dreambotName: string, varbits: { varbit: number; completedValue: number }[]): Quest {
        const quest = new Quest()
        quest.dreambotName = dreambotName
        quest.varbits = varbits
        return quest
    }

    static varbit(dreambotName: string, value: number, lastStepValue: number): Quest {
        const quest = new Quest()
        quest.dreambotName = dreambotName
        quest.varbit = value
        quest.lastStepValue = lastStepValue
        return quest
    }

    static varPlayer(dreambotName: string, value: number, lastStepValue: number, comparisonMode: 'equalOrGreaterThan' | 'equal' = 'equalOrGreaterThan'): Quest {
        const quest = new Quest()
        quest.dreambotName = dreambotName
        quest.varplayer = value
        quest.lastStepValue = lastStepValue
        quest.comparisonMode = comparisonMode
        return quest
    }
}
