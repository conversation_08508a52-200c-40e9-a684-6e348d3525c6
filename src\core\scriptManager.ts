import randomItem from 'random-item'
import { BotScript } from '../api/core/script/botScript'
import { Quest } from '../api/game/quests'
import { Skill } from '../api/game/skill'
import { AccountData, AccountProps } from '../api/model/accountData'
import { Random } from '../api/utils/random'
import { killCurrentProcess, log, shuffle } from '../api/utils/utils'
import { BotSettings } from '../botSettings'
import { AccountBuilder, TrainSkill, scriptsWithStop, toAccountBuilder, untilLevel } from '../scripts/account-trainer/accountBuilder'
import { Agility } from '../scripts/agility/agility'
import { AirOrbs } from '../scripts/air-orbs/airOrbs'
import { Alcher } from '../scripts/alcher/alcher'
import { BlastMining } from '../scripts/blast-mining/blastMining'
import { BondBuyer } from '../scripts/bond-buyer/bondBuyer'
import { FortisColosseum } from '../scripts/colosseum/colosseum'
import { CombatTrainer } from '../scripts/combat-trainer/combatTrainer'
import { SharkCooker } from '../scripts/cooking/cooker'
import { DbDefenders } from '../scripts/dreambot-scripts/dbDefenders'
import { DbMahoganyHomes } from '../scripts/dreambot-scripts/dbMahoganyHomes'
import { DbGuester } from '../scripts/dreambot-scripts/dbQuester'

import { FashionScapeScript } from '../scripts/fashion-scape/fashionScape'
import { SlotAction } from '../scripts/fashion-scape/states/fashionScapeMain'
import { FishingTrawler } from '../scripts/fishing-trawler/fishingTrawler'
import { Fletcher } from '../scripts/fletcher/fletcher'
import { FletcherConstants } from '../scripts/fletcher/fletcherConstants'
import { GreenDragons } from '../scripts/green-dragons/greenDragons'
import { HerbConstants } from '../scripts/herblore/constants'
import { Herblore } from '../scripts/herblore/herblore'
import { Hunter, HunterMethod } from '../scripts/hunter-trainer/hunter'
import { Miner } from '../scripts/miner/miner'
import { Minnows } from '../scripts/minnows/minnows'
import { MudCrafter } from '../scripts/mud-crafter/mudCrafter'
import { MuleReceiver } from '../scripts/muling/muleReceiver'
import { PlankMaker } from '../scripts/plank-maker/plankMaker'
import { PlankMakerSettings } from '../scripts/plank-maker/plankMakerSettings'
import { PrayerTrainer } from '../scripts/prayer/prayer'
import { DigSiteQuest } from '../scripts/quests/dig-site/digSite'
import { DruidicRitual } from '../scripts/quests/druidic-ritual/druidicRitual'
import { KarambwanCooker } from '../scripts/quests/karambwan-cooker/karambwanCooker'
import { KaramjaMediumDiary } from '../scripts/quests/karamja-medium/karamjaMedium'
import { TaiBwoTrio } from '../scripts/quests/tai-bwo-trio/taiBwoTrio'
import { Runecrafter } from '../scripts/runecrafter/runecrafter'
import { TabMaker } from '../scripts/tab-maker/tabMaker'
import { TestScript } from '../scripts/test/testScript'
import { Thiever } from '../scripts/thiever/thiever'
import { TutorialIsland } from '../scripts/tutorial-island/tutorialIsland'
import { RebinderScript } from '../scripts/utility/RebinderScript'
import { WebWalker } from '../scripts/web-walker/webWalker'
import { ScriptActiveTimeMonitor } from './background-tasks/scriptActiveTimeMonitor'
import { Bot } from './bot'
import { DbSubSlayer } from '../scripts/dreambot-scripts/dbSubSlayer'
import { WorldHopping } from '../api/game/worldHopping'
import { DbRQuester } from '../scripts/dreambot-scripts/dbRQuester'
import { TitheFarm } from '../scripts/tithe-farm/titheFarm'
import { RoguesDen } from '../scripts/rogues-den/roguesDen'
import { Mixology } from '../scripts/mixology/mixology'
import { BlastFurnace } from '../scripts/blast-furnace/blastFurnace'
import { CcBlastFurnaceFarm } from '../scripts/dreambot-scripts/dbCcBlastFurnaceFarm'
import { DbGGOTR } from '../scripts/dreambot-scripts/DbGGOTR'
import { SuperglassMaker as LunarCaster } from '../scripts/superglass-maker/lunarCaster'
import { PrepareAccType, PrepareForSale } from '../scripts/prepare-for-sale/prepareForSale'
import { Tanner } from '../scripts/tanner/tanner'
import { BehavioralSleep } from '../api/utils/behavior-fingerprinting/behavioralSleep'
import { BaggedPlants } from '../scripts/bagged-plants/baggedPlants'
import { StealingArtefacts } from '../scripts/stealing-artefacts/stealingArtefacts'



export class ScriptManager {
    static start() {



        let script = AccountData.current?.autostartScript

        if (BotSettings.devModeScript) {
            script = BotSettings.devModeScript
        }

        if (script.includes('_ignore')) {
            killCurrentProcess('Ignored script')
            return
        }

        let baseScript = script.split('_')[0]
        log('Starting autostart script: ', script)
        log('based on: ', baseScript)

        let requiredBuilderScripts: BotScript[] = []

        if (baseScript == 'DragonsOrFortis') {
            baseScript = this.mixedScripts(['GreenDragons', 'FortisColosseum'])
        }

        if (baseScript == 'RandomScripts1') {
            requiredBuilderScripts = [new TutorialIsland(), new BondBuyer(), ...this.getBasicBuilders(), ...this.accountTrainerGems()]
            baseScript = this.mixedScripts(['AirOrbs', 'GemsMiner', 'GreenDragons'])
        }

        if (baseScript == 'RandomScripts2') {
            requiredBuilderScripts = [new TutorialIsland(), new BondBuyer(), ...this.getBasicBuilders()]
            baseScript = this.mixedScripts(['KarambwanCooker', 'PlankMakerMahogany', 'GreenDragons'])
        }

        if (baseScript == 'RandomScripts3') {
            requiredBuilderScripts = [new TutorialIsland(), new BondBuyer(), ...this.getBasicBuilders(), ...this.accountTrainerGems()]
            baseScript = this.mixedScripts(['AirOrbs', 'GemsMiner', 'HunterChins'])
        }

        const startQueue = (...scripts: BotScript[]) => Bot.scriptHandler.startQueue(...requiredBuilderScripts, ...scripts)

        ScriptActiveTimeMonitor.baseScript = baseScript

        BotSettings.useBehavioralPointGeneration = true
        BotSettings.useLongBreaks = true
        BotSettings.useShortBreaks = true

        
        if (baseScript == 'TestScript') {
            startQueue(new TestScript())
            return
        }
       

        if (baseScript == 'Herblore') {
            if (script.includes('_toadflax')) HerbConstants.targetCombination = HerbConstants.TOADFLAX_POTIONS
            if (script.includes('_ranarr')) HerbConstants.targetCombination = HerbConstants.RANARR_POTIONS
            if (script.includes('_harralander')) HerbConstants.targetCombination = HerbConstants.HARRALANDER_POTIONS
            if (script.includes('_irit')) HerbConstants.targetCombination = HerbConstants.IRIT_POTIONS
            if (script.includes('_avantoe')) HerbConstants.targetCombination = HerbConstants.AVANTOE_POTIONS

            startQueue(
                new TutorialIsland(),
                new BondBuyer(),
                new DruidicRitual(),
                // new FashionScapeScript({
                //     wearBoots: SlotAction.WEAR,
                //     wearHelm: SlotAction.WEAR,
                //     wearShield: SlotAction.WEAR,
                //     wearSword: SlotAction.WEAR,
                //     wearChest: SlotAction.WEAR,
                //     wearLegs: SlotAction.WEAR,
                //     wearGloves: SlotAction.WEAR,
                // }),
                new Herblore(false)
            )
            return
        }

        if (baseScript == 'TaiBwoTrio') {
            startQueue(new BondBuyer(), new TaiBwoTrio())
            return
        }

        if (baseScript == 'TitansBuilder') {
            startQueue(new TutorialIsland(), new BondBuyer(),
            
            ...skill(Skill.MAGIC, 59),
            ...skill(Skill.PRAYER, 70),
            
            ...accountTrainerCombatStats(20, 20, 20),
            quests(Quest.WATERFALL_QUEST, Quest.DRUIDIC_RITUAL),
            ...accountTrainerCombatStats(70, 70, 70),

            ...skill(Skill.RANGE, 70),
            ...skill(Skill.AGILITY, 36),
            ...skill(Skill.CRAFTING, 25),
            ...skill(Skill.HERBLORE, 18),
            ...skill(Skill.SMITHING, 30),
       
            quests(  
                Quest.BLACK_KNIGHTS_FORTRESS,
                Quest.HOLY_GRAIL,
                Quest.MERLINS_CRYSTAL,
                Quest.ONE_SMALL_FAVOUR,
                Quest.RUNE_MYSTERIES,
                Quest.SHILO_VILLAGE,
                Quest.JUNGLE_POTION,
                Quest.DRUIDIC_RITUAL,
                Quest.MURDER_MYSTERY,
                Quest.KINGS_RANSOM,
                Quest.KNIGHT_WAVES_TRAINING_GROUNDS
            ),

            new RebinderScript("TitansBuilder_ready_ignore")
        )
            return
        }
         if (script.startsWith('HunterKitCaster')) {
            startQueue(new TutorialIsland(), new BondBuyer(),
            ...this.accountTrainerSkiller(),

           ...accountTrainerCombatStats(20, 20, 20),
            ...skill(Skill.PRAYER, 43),
            ...skill(Skill.MAGIC, 67),
            
            quests( Quest.WATERFALL_QUEST, Quest.DRUIDIC_RITUAL),
            
            
            ...skill(Skill.HERBLORE, 5),
            ...skill(Skill.CRAFTING, 61),
            ...skill(Skill.FIREMAKING, 49),
            ...skill(Skill.MINING, 60),
            ...skill(Skill.WOODCUTTING, 55),
            ...skill(Skill.FLETCHING, 25),
            ...skill(Skill.AGILITY, 32),
            ...skill(Skill.RUNECRAFTING, 30),
            
            quests( Quest.THE_FREMENNIK_TRIALS, Quest.LOST_CITY, Quest.RUNE_MYSTERIES, Quest.JUNGLE_POTION,
                Quest.SHILO_VILLAGE, Quest.RUNE_MYSTERIES, Quest.ENTER_THE_ABYSS,  Quest.TEMPLE_OF_THE_EYE, Quest.LUNAR_DIPLOMACY),
                
                ...teleAlchTrainer( 71),
                ...accountTrainerCombatStats(70, 70, 70),

                 quests(Quest.EADGARS_RUSE, Quest.DRUIDIC_RITUAL, Quest.TROLL_STRONGHOLD, Quest.DEATH_PLATEAU, Quest.DREAM_MENTOR),
                 new LunarCaster()

            )
            return
        }

        if (script.startsWith('RunecraftMoneymaker')) {
            startQueue(new TutorialIsland(), new BondBuyer(),
            ...this.accountTrainerSkiller(),

            ...skill(Skill.STRENGTH, 10),
            ...skill(Skill.ATTACK, 10),
            ...skill(Skill.DEFENSE, 10),
            ...skill(Skill.ATTACK, 20),
            ...skill(Skill.STRENGTH, 20),
            ...skill(Skill.DEFENSE, 20),
            ...skill(Skill.PRAYER, 43),
            ...skill(Skill.MAGIC, 67),
            
            quests( Quest.WATERFALL_QUEST, Quest.DRUIDIC_RITUAL),
            
            ...skill(Skill.ATTACK, 30),
            ...skill(Skill.STRENGTH, 30),
            ...skill(Skill.DEFENSE, 30),
         
            ...skill(Skill.ATTACK, 40),
            ...skill(Skill.STRENGTH, 40),
            ...skill(Skill.DEFENSE, 40),
         
            ...skill(Skill.HERBLORE, 5),
            ...skill(Skill.CRAFTING, 61),
            ...skill(Skill.FIREMAKING, 49),
            ...skill(Skill.MINING, 60),
            ...skill(Skill.WOODCUTTING, 55),
            ...skill(Skill.FLETCHING, 25),
            ...skill(Skill.AGILITY, 32),
            ...skill(Skill.RUNECRAFTING, 30),
            
            quests( Quest.THE_FREMENNIK_TRIALS, Quest.LOST_CITY, Quest.RUNE_MYSTERIES, Quest.JUNGLE_POTION,
                Quest.SHILO_VILLAGE, Quest.RUNE_MYSTERIES, Quest.ENTER_THE_ABYSS,  Quest.TEMPLE_OF_THE_EYE, Quest.LUNAR_DIPLOMACY),
                
             new DbGGOTR(),         

            new RebinderScript("RunecraftMoneymaker_ready_ignore")
        )
            return
        }

        if (script.startsWith('AccountTrainer_powermining')) {
            BotSettings.useBehavioralSleeping = true


            startQueue(
                new TutorialIsland(),
                new BondBuyer(),
                ...this.accountTrainerSkiller(),
                // new FashionScapeScript({
                    // getBootsOfLightness: true,
                    // wearBoots: SlotAction.IGNORE,
                    // wearHelm: SlotAction.IGNORE,
                    // wearCape: SlotAction.IGNORE,
                    // wearShield: SlotAction.IGNORE,
                    // wearSword: SlotAction.IGNORE,
                    // wearChest: SlotAction.IGNORE,
                    // wearLegs: SlotAction.IGNORE,
                // }),
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 19)),
                new AccountBuilder({
                    scripts: [new Miner(false)],
                    skill: Skill.MINING,
                    trainUntilLevel: 99,
                }),
                new RebinderScript('AccountTrainerDone_powermining_ignore')
            )
            return
        }

      

            if (script.startsWith('AccountTrainer_gmauler-90')) {

            const train = [
                new BondBuyer(),
                ...this.accountTrainerGmaulerBegginingF2p(),
                ...this.accountTrainerGmaulerBasics(),

                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 90)),
                ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 90)),
            ]

            startQueue(new TutorialIsland(), ...train, ...prepareForSale("god-books", "mith-gloves"), new RebinderScript('AccountTrainerDone_gmauler-80_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_gmauler-80')) {

            const train = [
                new BondBuyer(),
                ...this.accountTrainerGmaulerBegginingF2p(),
                ...this.accountTrainerGmaulerBasics(),

                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 80)),
                ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 80)),
            ]

            startQueue(new TutorialIsland(), ...train, ...prepareForSale("god-books", "mith-gloves"), new RebinderScript('AccountTrainerDone_gmauler-80_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_gmauler-70')) {

            const train = [new BondBuyer(), ...this.accountTrainerGmaulerBegginingF2p(), ...this.accountTrainerGmaulerBasics()]

            startQueue(new TutorialIsland(), ...train, ...prepareForSale("god-books", "mith-gloves"), new RebinderScript('AccountTrainerDone_gmauler-70_ignore'))
            return
        }

        if (baseScript == 'HunterChins') {
            startQueue(
                new TutorialIsland(),
                new BondBuyer(),

                // new FashionScapeScript({
                    // getBootsOfLightness: true,
                    // wearBoots: SlotAction.WEAR,
                    // wearHelm: SlotAction.WEAR,
                    // wearCape: SlotAction.WEAR,
                    // wearShield: SlotAction.IGNORE,
                    // wearSword: SlotAction.IGNORE,
                    // wearChest: SlotAction.WEAR,
                    // wearLegs: SlotAction.WEAR,
                // }),

                ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 63)),

                new DbGuester(Quest.EAGLES_PEAK),

                new Hunter(true)
            )
            return
        }

        if (script.startsWith('AccountTrainer_farming')) {
            //prettier-ignore
            const train = [
                ...this.accountTrainerSkiller(), 
                ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 34)),
                ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 99, "tithe-farm"))
            ]

            startQueue(new TutorialIsland(), new BondBuyer(), ...train,  new RebinderScript('AccountTrainerDone_farming_ignore'))

            return
        }

        if (script.startsWith('AccountTrainer_thieving')) {
            BotSettings.useBehavioralSleeping = true
            startQueue(
                new TutorialIsland(),
                new BondBuyer(),
                ...this.accountTrainerSkiller(),
                // ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 50)),
                // ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 50)),
                ...skill(Skill.THIEVING, 99),
                new RebinderScript('AccountTrainerDone_thieving_ignore')
            )

            return
        }

          if (script.startsWith('AccountTrainer_MasterFarmer')) {
            startQueue(
                new TutorialIsland(),
                new BondBuyer(),
                ...this.accountTrainerSkiller(),
                ...skill(Skill.THIEVING, 50),
                ...skill(Skill.AGILITY, 50),
                ...skill(Skill.FARMING, 72),
                new RebinderScript('AccountTrainerDone_MasterFarmer_ignore') 
            )

            return
        }

        if (script.startsWith('AccountTrainer_hunter')) {
            startQueue(
                new TutorialIsland(),
                new BondBuyer(),
                ...this.accountTrainerSkiller(),
                // new FashionScapeScript({
                //     getBootsOfLightness: true,
                //     wearBoots: SlotAction.WEAR,
                //     wearHelm: SlotAction.WEAR,
                //     wearCape: SlotAction.WEAR,
                //     wearShield: SlotAction.IGNORE,
                //     wearSword: SlotAction.IGNORE,
                //     wearChest: SlotAction.WEAR,
                //     wearLegs: SlotAction.WEAR,
                // }),

                ...toAccountBuilder({ skill: Skill.HUNTER, trainUntilLevel: 67, scripts: [new Hunter(false, HunterMethod.FALCONRY)] }),
                ...toAccountBuilder({ skill: Skill.HUNTER, trainUntilLevel: 99, scripts: [new Hunter(false, HunterMethod.RED_SALAMANDERS)] }),

                new RebinderScript('AccountTrainerDone_hunter_ignore')
            )
            return
        }

       
        if (script.startsWith('Mixology')) {
            const scripts = [
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 81, 'expensive')),
                new DbGuester(Quest.CHILDREN_OF_THE_SUN),
                new Mixology(),
            ]
            startQueue(new TutorialIsland(), new BondBuyer(), ...scripts)
            return 
        }

     if (script.startsWith('AccountTrainer_herblore')) {
            const scripts = [
                ...this.accountTrainerSkiller(),
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 99)),
                 new RebinderScript('AccountTrainerDone_herblore_ignore')
            ]
            startQueue(new TutorialIsland(), new BondBuyer(), ...scripts)
            return 
        }
     

        if (script.startsWith('AccountTrainer_skiller-fp2p')) {

            const trainP2p = [
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 40)),

                ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.MINING, 40)),

                ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 20)),
                ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 40)),
            ]

            startQueue(new TutorialIsland(), new BondBuyer(), ...trainP2p, new RebinderScript('AccountTrainerDone_skiller-fp2p_ignore'))
            return
        }

        if (script.startsWith('StealingArtefacts')) {
              startQueue(new TutorialIsland(), new BondBuyer(), new StealingArtefacts())
            return
        }
        if (script.startsWith('Firemaking')) {
                const train = [
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 99)),
            ]
            startQueue(new TutorialIsland(), ...train)
            return 
        }
        if (script.startsWith('AccountTrainer_skiller-f2p')) {

            const train = [
                ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.MINING, 40)),
            ]

            startQueue(new TutorialIsland(), ...train, new RebinderScript('AccountTrainerDone_skiller-f2p_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_skiller-p2p')) {

            const train = [
                ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 40)),

                ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 20)),
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 40)),
            ]

            startQueue(new TutorialIsland(), new BondBuyer(), ...train, new RebinderScript('AccountTrainerDone_skiller-p2p_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_skiller-60')) {
 BotSettings.useBehavioralSleeping = true
            const train = rotatedSkiller([Skill.RUNECRAFTING, Skill.COOKING, Skill.THIEVING, Skill.FIREMAKING, Skill.FLETCHING, Skill.CRAFTING,
                             Skill.WOODCUTTING, Skill.AGILITY, Skill.SMITHING, Skill.FISHING,
                              Skill.HUNTER, Skill.WOODCUTTING, Skill.MINING, Skill.HERBLORE, Skill.FARMING], 60)

            

            startQueue(new TutorialIsland(), new BondBuyer(), ...train, new RebinderScript('AccountTrainerDone_skiller-60_ignore'))
            return
        }
        if (script.startsWith('AccountTrainer_skiller-70')) {

            const train = [
                ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.MINING, 50)),
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 50)),

                ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.MINING, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 60)),

                ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.MINING, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 70)),

                ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 40)),
            ]

            startQueue(new TutorialIsland(), new BondBuyer(), ...train, new RebinderScript('AccountTrainerDone_skiller-p2p_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_agility')) {
            startQueue(
                new TutorialIsland(),
                new BondBuyer(),
                ...this.accountTrainerSkiller(),
                // new FashionScapeScript({
                    // getBootsOfLightness: true,
                    // wearBoots: SlotAction.IGNORE,
                    // wearHelm: SlotAction.IGNORE,
                    // wearCape: SlotAction.IGNORE,
                    // wearShield: SlotAction.IGNORE,
                    // wearSword: SlotAction.IGNORE,
                    // wearChest: SlotAction.IGNORE,
                    // wearLegs: SlotAction.IGNORE,
                // }),
                new AccountBuilder({
                    scripts: [new Agility()],
                    skill: Skill.AGILITY,
                    trainUntilLevel: 99,
                }),
                new RebinderScript('AccountTrainerDone_agility_ignore')
            )
            return
        }

        if (baseScript == 'TabMaker') {
            let fashionScapeCurrent = Random.nextItem(AccountData.seed(), [
                {
                    getBootsOfLightness: true,
                    skillcapeSkill: Random.nextItem(AccountData.seed(), [Skill.COOKING, Skill.MAGIC, Skill.MINING]),
                    wearSword: SlotAction.IGNORE,
                    wearCape: SlotAction.WEAR,
                    wearBoots: SlotAction.WEAR,
                    wearHelm: SlotAction.UNEQUIP,
                    wearShield: SlotAction.UNEQUIP,
                    wearChest: SlotAction.UNEQUIP,
                    wearLegs: SlotAction.UNEQUIP,
                },
                {
                    getBootsOfLightness: false,
                    wearSword: SlotAction.IGNORE,
                    wearCape: SlotAction.UNEQUIP,
                    wearBoots: SlotAction.WEAR,
                    wearHelm: SlotAction.UNEQUIP,
                    wearShield: SlotAction.UNEQUIP,
                    wearChest: SlotAction.WEAR,
                    wearLegs: SlotAction.WEAR,
                },
                {
                    getBootsOfLightness: true,
                    wearSword: SlotAction.IGNORE,
                    wearCape: SlotAction.UNEQUIP,
                    wearBoots: SlotAction.WEAR,
                    wearHelm: SlotAction.WEAR,
                    wearShield: SlotAction.UNEQUIP,
                    wearChest: SlotAction.UNEQUIP,
                    wearLegs: SlotAction.UNEQUIP,
                },
                {
                    getBootsOfLightness: true,
                    wearCape: Random.nextItem(AccountData.seed(1), [SlotAction.UNEQUIP, SlotAction.WEAR, SlotAction.IGNORE]),
                    wearBoots: Random.nextItem(AccountData.seed(2), [SlotAction.UNEQUIP, SlotAction.WEAR, SlotAction.IGNORE]),
                    wearSword: SlotAction.IGNORE,
                    wearHelm: Random.nextItem(AccountData.seed(3), [SlotAction.UNEQUIP, SlotAction.WEAR, SlotAction.IGNORE]),
                    wearShield: Random.nextItem(AccountData.seed(4), [SlotAction.UNEQUIP, SlotAction.WEAR, SlotAction.IGNORE]),
                    wearChest: Random.nextItem(AccountData.seed(5), [SlotAction.UNEQUIP, SlotAction.WEAR, SlotAction.IGNORE]),
                    wearLegs: Random.nextItem(AccountData.seed(6), [SlotAction.UNEQUIP, SlotAction.WEAR, SlotAction.IGNORE]),
                },
            ])

            startQueue(new BondBuyer(), ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, 40)), new TabMaker())
            return
        }

        if (baseScript == 'TutorialIsland') {
            startQueue(new TutorialIsland())
            return
        }

        if (baseScript == 'Thiever') {
            startQueue(new TutorialIsland(), new BondBuyer(), new Thiever())
            return
        }

        if (baseScript == 'KaramjaMediumDiary') {
            startQueue(new BondBuyer(), new KaramjaMediumDiary())
            return
        }

        if (baseScript == 'GemsMiner') {
            startQueue(
                new TutorialIsland(),
                new BondBuyer(),

                ...this.accountTrainerGems(),

                // new FashionScapeScript({
                //     getBootsOfLightness: true,
                //     wearBoots: SlotAction.WEAR,
                //     wearHelm: SlotAction.WEAR,
                //     wearGloves: SlotAction.IGNORE,
                //     wearChest: SlotAction.UNEQUIP,
                //     wearLegs: SlotAction.UNEQUIP,
                //     wearShield: SlotAction.UNEQUIP,
                //     wearSword: SlotAction.UNEQUIP,
                // }),
                new Miner(true)
            )
            return
        }

        // const fashionScapeBasic = new FashionScapeScript({
        //     getBootsOfLightness: true,
        //     wearHelm: SlotAction.WEAR,
        //     wearChest: SlotAction.WEAR,
        //     wearLegs: SlotAction.WEAR,
        //     wearGloves: SlotAction.IGNORE,
        //     wearShield: SlotAction.UNEQUIP,
        //     wearSword: SlotAction.IGNORE,
        //     wearBoots: SlotAction.WEAR,
        // })

        if (script == 'MuleReceiver_1') {
            startQueue(new TutorialIsland(), new BondBuyer(), new MuleReceiver(MuleReceiver.package1))
            return
        }

        if (script == 'MuleReceiver_2') {
            startQueue(new TutorialIsland(), new BondBuyer(), new MuleReceiver(MuleReceiver.package2))
            return
        }

        if (script == 'MuleReceiver_3') {
            startQueue(new TutorialIsland(), new BondBuyer(), new MuleReceiver(MuleReceiver.package3))
            return
        }
        if (script == 'MuleReceiver_builders') {
            startQueue(new TutorialIsland(), new BondBuyer(), new MuleReceiver(MuleReceiver.packageBuilders))
            return
        } 
          if (script == 'MuleReceiver_exclusive') {
            startQueue(new TutorialIsland(), new BondBuyer(), new MuleReceiver(MuleReceiver.packageBuilders))
            return
        }

        if (baseScript == 'FortisColosseum') {
            WorldHopping.preferLowPingWorlds = true
            startQueue(new BondBuyer(), new FortisColosseum())
            return
        }

        if (baseScript == 'Alcher') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new Alcher())
            return
        }

        if (baseScript == 'Fletcher') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new Fletcher(FletcherConstants.longbowsMagic))
            return
        }

        if (script == 'Tanner_Green') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new Tanner('greenDragonhide'))
            return
        }

        if (script == 'Tanner_Blue') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new Tanner('blueDragonhide'))
            return
        }

        if (script == 'Tanner_Red') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new Tanner('redDragonhide'))
            return
        }

        if (baseScript == 'FletcherYew') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new Fletcher(FletcherConstants.longbowsYew))
            return
        }

        if (baseScript == 'PlankMakerMahogany') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new PlankMaker(PlankMakerSettings.Mahogany))
            return
        }

        if (baseScript == 'PlankMakerOak') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new PlankMaker(PlankMakerSettings.Oak))
            return
        }
        if (baseScript == 'PlankMakerTeak') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new PlankMaker(PlankMakerSettings.Teak))
            return
        }
        if (baseScript == 'PlankMakerRegular') {
            startQueue(new TutorialIsland(), new BondBuyer(),  new PlankMaker(PlankMakerSettings.Regular))
            return
        }
        if (baseScript == 'AirOrbs') {
            startQueue(new TutorialIsland(), new BondBuyer(), new Alcher(66), ...toAccountBuilder(TrainSkill.of(Skill.PRAYER, 43)), new AirOrbs())
            return
        }
        if (baseScript == 'Prayer') {
            startQueue(new PrayerTrainer())
            return
        }
        if (baseScript == 'MudRunes') {
            startQueue(new TutorialIsland(), new BondBuyer(), new Runecrafter(13),  new MudCrafter())
            return
        }
        if (baseScript == 'Agility') {
            startQueue(new Agility())
            return
        }
        if (baseScript == 'CombatTrainer') {
            startQueue(new CombatTrainer(false))
            return
        }
        if (baseScript == 'Digsite') {
            startQueue(new DigSiteQuest())
            return
        }

        if (baseScript == 'Test') {
            startQueue(new WebWalker())
            return
        }
        if (baseScript == 'Hunter') {
            startQueue(new TutorialIsland(), new BondBuyer(), new Hunter())
            return
        }

        if (baseScript == 'GreenDragons') {
            startQueue(new BondBuyer(), ...this.getBasicBuilders(), new GreenDragons())
            return
        }

        if (baseScript == 'BlastFurnaceTest') {
            startQueue(new BlastFurnace())
            return
        }

        if (baseScript == 'BlastFurnace') {
            startQueue(
                new TutorialIsland(),
                new BondBuyer(),
              ...skill(Skill.PRAYER, 43),
              ...skill(Skill.MAGIC, 41),
              ...skill(Skill.SMITHING, 30),
              ...skill(Skill.MINING, 30),
              new CcBlastFurnaceFarm(),
              new BlastFurnace()
        )
            return
        }

        if (baseScript == 'BlastMining') {
            // const fashionScape = new FashionScapeScript({
            //     getBootsOfLightness: true,
            //     wearBoots: SlotAction.WEAR,
            //     wearHelm: SlotAction.WEAR,
            //     wearShield: SlotAction.UNEQUIP,
            //     wearSword: SlotAction.UNEQUIP,
            //     wearChest: SlotAction.WEAR,
            //     wearLegs: SlotAction.WEAR,
            // })

            const mining = [...toAccountBuilder(TrainSkill.of(Skill.MINING, 43))]
            startQueue(new BondBuyer(), ...mining,  new BlastMining())
            return
        }

        if (baseScript == 'KarambwanCooker') {
            WorldHopping.preferLowPingWorlds = true

            //Karambwan builder
            const cooking = [
                ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 15)),
                ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 5)),
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 30)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 5)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 5)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 5)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 5)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 15)),
                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 20)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 20)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 20)),

                new DbGuester(Quest.WATERFALL_QUEST),

                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 33)),
                new DbGuester(Quest.WITCHS_HOUSE),
                new DbGuester(Quest.DRUIDIC_RITUAL, Quest.JUNGLE_POTION, Quest.TAI_BWO_WANNAI_TRIO),
            ]

            // const fashionScape = new FashionScapeScript({
            //     getBootsOfLightness: true,
            //     skillcapeSkill: Skill.COOKING,
            //     wearBoots: SlotAction.WEAR,
            //     wearHelm: SlotAction.IGNORE,
            //     wearShield: SlotAction.UNEQUIP,
            //     wearSword: SlotAction.UNEQUIP,
            //     wearChest: SlotAction.WEAR,
            //     wearLegs: SlotAction.WEAR,
            // })

            startQueue(new TutorialIsland(), new BondBuyer(), ...cooking,  new KarambwanCooker())
            return
        }

        if (baseScript == 'SharkCooker') {
            //Karambwan builder
            const cooking = toAccountBuilder(TrainSkill.of(Skill.COOKING, 30))
            const agility = toAccountBuilder(TrainSkill.of(Skill.AGILITY, 15))
            const fishing = toAccountBuilder(TrainSkill.of(Skill.FISHING, 5))

            const cookingKarambwansUntil99 = toAccountBuilder({
                scripts: [new KarambwanCooker()],
                skill: Skill.COOKING,
                trainUntilLevel: 99,
            })

            const questsGuester = new DbGuester(Quest.DRUIDIC_RITUAL, Quest.JUNGLE_POTION)
            const questsSubQuester = new TaiBwoTrio()

            // const fashionScape = new FashionScapeScript({
            //     getBootsOfLightness: true,
            //     skillcapeSkill: Skill.COOKING,
            //     wearBoots: SlotAction.WEAR,
            //     wearHelm: SlotAction.IGNORE,
            //     wearShield: SlotAction.UNEQUIP,
            //     wearSword: SlotAction.UNEQUIP,
            //     wearChest: SlotAction.WEAR,
            //     wearLegs: SlotAction.WEAR,
            // })

            startQueue(new BondBuyer(), ...cooking, ...agility, ...fishing, questsGuester, questsSubQuester, ...cookingKarambwansUntil99, new SharkCooker())
            return
        }

        if (baseScript == 'Minnows') {
            const herblore = [new DruidicRitual(), ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 3))] //allows for 3ticking
            const fishing = [...toAccountBuilder(TrainSkill.of(Skill.FISHING, 82))]
            const questsGuester = new DbGuester(Quest.FISHING_CONTEST)

            // const fashionScape = new FashionScapeScript({
            //     getBootsOfLightness: true,
            //     skillcapeSkill: Skill.COOKING,
            //     wearBoots: SlotAction.WEAR,
            //     wearHelm: SlotAction.IGNORE,
            //     wearShield: SlotAction.UNEQUIP,
            //     wearSword: SlotAction.UNEQUIP,
            //     wearChest: SlotAction.WEAR,
            //     wearLegs: SlotAction.WEAR,
            // })

            startQueue(new BondBuyer(), ...herblore, ...fishing, questsGuester, new FishingTrawler(),  new Minnows())
            return
        }

        if (script.startsWith('AccountTrainer_db')) {

            startQueue(new TutorialIsland(), new BondBuyer(), ...this.getBasicBuilders(), new RebinderScript('AccountTrainerDone_db_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_mobile_p2p')) {

            const train = [
                ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, 25)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 5)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 5)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 10)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 10)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 10)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 25)),

                ...toAccountBuilder(TrainSkill.of(Skill.MINING, 25)),

                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 25)),
                ...toAccountBuilder(TrainSkill.of(Skill.PRAYER, 25)),

                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.PRAYER, 40)),

                ...toAccountBuilder(TrainSkill.of(Skill.MINING, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, 40)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 40)),
                ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 40)),

                new DruidicRitual(),
                ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 40)),
            ]

            startQueue(new TutorialIsland(), new BondBuyer(), ...train, new RebinderScript('AccountTrainerDone_mobile_p2p_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_mobile')) {

            const trainFree = [
                ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, 30)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 5)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 5)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 10)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 10)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 10)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 30)),

                ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 30)),
                ...toAccountBuilder(TrainSkill.of(Skill.MINING, 30)),
            ]

            startQueue(new TutorialIsland(), ...trainFree, new RebinderScript('AccountTrainerDone_mobile_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_fortis')) {
            WorldHopping.preferLowPingWorlds = true

            const additionalSteps = [
                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 55)),

                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 65)),
                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 65)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 60)),
                ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 40)),

                new DbGuester(Quest.THE_FREMENNIK_ISLES),
                new DbDefenders(),

                ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 65)),

                ...toAccountBuilder(TrainSkill.of(Skill.PRAYER, 70)),
                ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 36)),
                ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, 45)),

                new DbGuester(
                    Quest.LOST_CITY,
                    Quest.CHILDREN_OF_THE_SUN,
                    Quest.BLACK_KNIGHTS_FORTRESS,
                    Quest.HOLY_GRAIL,
                    Quest.MERLINS_CRYSTAL,
                    Quest.ONE_SMALL_FAVOUR,
                    Quest.RUNE_MYSTERIES,
                    Quest.SHILO_VILLAGE,
                    Quest.JUNGLE_POTION,
                    Quest.DRUIDIC_RITUAL,
                    Quest.MURDER_MYSTERY,
                    Quest.KINGS_RANSOM,
                    Quest.KNIGHT_WAVES_TRAINING_GROUNDS
                ),
            ]

            startQueue(new TutorialIsland(), new BondBuyer(), ...this.getBasicBuilders(), ...additionalSteps, new RebinderScript('AccountTrainerDone_fortis_ignore'))
            return
        }

        if (script.startsWith('AccountTrainer_gems')) {

            startQueue(new TutorialIsland(), new BondBuyer(), ...this.accountTrainerGems(), new RebinderScript('AccountTrainerDone_gems_ignore'))
            return
        }
    }
    static mixedScripts(mixedScripts: string[]): string {
        let recent = AccountProps.getOrDefault('randomScript.recent', '')
        let recentSelectionTime = AccountProps.getOrDefault('randomScript.recentSelectionTime', -1)
        let toStart = ''

        if (!mixedScripts.includes(recent)) {
            recentSelectionTime = -1
        }

        if (recentSelectionTime == -1) {
            toStart = randomItem(mixedScripts)
            AccountProps.set('randomScript.recent', toStart)
            AccountProps.set('randomScript.recentSelectionTime', ScriptActiveTimeMonitor.getScriptSeconds(toStart))
            log('Picked new random script: ', toStart)
            return toStart
        }

        if (ScriptActiveTimeMonitor.getScriptSeconds(recent) - recentSelectionTime < 60 * 120) {
            log('Picked last script: ', recent)
            return recent
        }

        toStart = randomItem(mixedScripts.filter((script) => script != recent))
        AccountProps.set('randomScript.recent', toStart)
        AccountProps.set('randomScript.recentSelectionTime', ScriptActiveTimeMonitor.getScriptSeconds(toStart))
        log('Picked new random script, recent: ', recent, ' | new toStart: ', toStart)

        return toStart
    }

    static questBoneVoyageCombat(): BotScript[] {
        return [
            ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 10)),
            ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 10)),
            ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 25)),

            new DbGuester(
                Quest.THE_DIG_SITE,

                //Farm kudos:
                Quest.RUNE_MYSTERIES,
                Quest.PRIEST_IN_PERIL,
                Quest.DEMON_SLAYER,
                Quest.HAZEEL_CULT,
                Quest.THE_RESTLESS_GHOST,
                Quest.MAKING_HISTORY,

                //Fix for these varps:
                Quest.CLEAN_MUSEUM_FINDS,
                Quest.VARROCK_MUESEUM,

                Quest.BONE_VOYAGE
            ),
        ]
    }

    static accountTrainerGems(): BotScript[] {
        const additionalSteps = [
            new DbGuester(Quest.WITCHS_HOUSE, Quest.WATERFALL_QUEST, Quest.VAMPYRE_SLAYER, Quest.DORICS_QUEST, Quest.BLACK_KNIGHTS_FORTRESS, Quest.GOBLIN_DIPLOMACY, Quest.ERNEST_THE_CHICKEN),

            ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 27)),
            new KaramjaMediumDiary(true),
            ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 35)),
            ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 65)),
            ...toAccountBuilder(TrainSkill.of(Skill.MINING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 50)),
            ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, 17)),
            ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 43)),
            ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 40)),

            new DbGuester(Quest.THE_GRAND_TREE, Quest.DRUIDIC_RITUAL, Quest.JUNGLE_POTION, Quest.SHILO_VILLAGE, Quest.KARAMJA_EASY, Quest.DRAGON_SLAYER_I),
            new TaiBwoTrio(),
            new KaramjaMediumDiary(),
        ]
        return additionalSteps
    }

    static accountTrainerSkiller(): BotScript[] {
        const trainP2p = [
            ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 40)),

            ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 40)),
            ...toAccountBuilder(TrainSkill.of(Skill.MINING, 40)),

            ...toAccountBuilder(TrainSkill.of(Skill.FARMING, 20)),
            ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, 40)),
        ]
        return trainP2p
    }

    static getBasicBuilders(): BotScript[] {
        const scriptsStep0 = [new DbGuester(Quest.IMP_CATCHER, Quest.WITCHS_HOUSE, Quest.WATERFALL_QUEST, Quest.DORICS_QUEST, Quest.TREE_GNOME_VILLAGE, Quest.THE_KNIGHTS_SWORD, Quest.VAMPYRE_SLAYER)]

        const scriptsStep1 = [...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 10)), ...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, 20)), ...toAccountBuilder(TrainSkill.of(Skill.HITPOINTS, 25))]

        const scriptsStep2 = [
            new DbGuester(
                Quest.THE_TOURIST_TRAP,
                Quest.SLEEPING_GIANTS,
                Quest.FIGHT_ARENA,
                Quest.MERLINS_CRYSTAL,
                Quest.HOLY_GRAIL,
                Quest.THE_GRAND_TREE,
                Quest.GERTRUDES_CAT,
                Quest.ICTHLARINS_LITTLE_HELPER,
                Quest.DRUIDIC_RITUAL
            ),
        ]

        const scriptsStep3 = [
            ...toAccountBuilder(TrainSkill.of(Skill.PRAYER, Random.nextSeed(AccountData.seed() + 15, 43, 50))),
            ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 10)),
            ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 30)),
            ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 25)),
        ]

        const scriptsStep4 = [
            new DbGuester(
                Quest.DRAGON_SLAYER_I,
                Quest.X_MARKS_THE_SPOT,
                Quest.CLIENT_OF_KOUREND,
                Quest.SEA_SLUG,
                Quest.THE_DIG_SITE,
                Quest.THE_FREMENNIK_TRIALS,
                Quest.PLAGUE_CITY,
                Quest.BIOHAZARD,
                Quest.DWARF_CANNON
            ),
        ]

        const scriptsStep5 = [...toAccountBuilder(TrainSkill.of(Skill.HITPOINTS, 30))]

        const scriptsStep6 = [new DbGuester(Quest.MONKEY_MADNESS_I)]

        // const scriptsStep7 = [new DbMahoganyHomes()]

        const scriptsStep8 = [
            ...toAccountBuilder(TrainSkill.of(Skill.PRAYER, Random.nextSeed(AccountData.seed() + 1, 43, 50))),
            ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, Random.nextSeed(AccountData.seed() + 2, 30, 40))),
            ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, Random.nextSeed(AccountData.seed() + 3, 30, 40))),
            ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, Random.nextSeed(AccountData.seed() + 4, 30, 40))),
            ...toAccountBuilder(TrainSkill.of(Skill.COOKING, Random.nextSeed(AccountData.seed() + 5, 30, 40))),
            ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, Random.nextSeed(AccountData.seed() + 6, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.RANGE, Random.nextSeed(AccountData.seed() + 7, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, Random.nextSeed(AccountData.seed() + 8, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, Random.nextSeed(AccountData.seed() + 9, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, Random.nextSeed(AccountData.seed() + 10, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.MINING, Random.nextSeed(AccountData.seed() + 11, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, Random.nextSeed(AccountData.seed() + 12, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.FISHING, Random.nextSeed(AccountData.seed() + 13, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, Random.nextSeed(AccountData.seed() + 14, 30, 35))),
            ...toAccountBuilder(TrainSkill.of(Skill.FARMING, Random.nextSeed(AccountData.seed() + 15, 20, 25))),
            ...toAccountBuilder(TrainSkill.of(Skill.HUNTER, Random.nextSeed(AccountData.seed() + 16, 20, 25))),
        ]

        return [...scriptsStep0, ...scriptsStep1, ...scriptsStep2, ...scriptsStep3, ...scriptsStep4, ...scriptsStep5, ...scriptsStep6, ...scriptsStep8]
    }

    static accountTrainerGmaulerBasics() {
        return [
            ...toAccountBuilder(TrainSkill.of(Skill.MINING, 30)),

            ...toAccountBuilder(TrainSkill.of(Skill.THIEVING, 53)),
            ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 20)),
            ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 10)),
            ...toAccountBuilder(TrainSkill.of(Skill.AGILITY, 35)),
            ...toAccountBuilder(TrainSkill.of(Skill.FLETCHING, 10)),
            ...toAccountBuilder(TrainSkill.of(Skill.HERBLORE, 10)),

            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 50)),
            ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 50)),
            ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 41)),

            new DbGuester(
                Quest.PRIEST_IN_PERIL,
            ),

            
            new DbSubSlayer(19),

            new DbGuester(
                Quest.WATERFALL_QUEST,
                Quest.DEATH_PLATEAU,
                Quest.FIGHT_ARENA,
                Quest.THE_GRAND_TREE,
                Quest.TREE_GNOME_VILLAGE,
                Quest.PRIEST_IN_PERIL,
                Quest.ERNEST_THE_CHICKEN,
                Quest.THE_RESTLESS_GHOST,
                Quest.ANIMAL_MAGNETISM
            ),

            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 60)),
            ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 60)),

            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 65)),
            ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 65)),

            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 70)),
            ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 70)),
            
            new DbGuester(
                Quest.MOUNTAIN_DAUGHTER,
                Quest.COOKS_ASSISTANT,
                Quest.GOBLIN_DIPLOMACY,
                
                Quest.VAMPYRE_SLAYER,
                Quest.DEMON_SLAYER,
                Quest.DRUIDIC_RITUAL,
                Quest.GERTRUDES_CAT,
                
                Quest.FISHING_CONTEST,
                Quest.BIG_CHOMPY_BIRD_HUNTING,
                Quest.THE_GOLEM,
                
                Quest.SHADOW_OF_THE_STORM,
                
                Quest.RECIPE_FOR_DISASTER_START,
                Quest.RECIPE_FOR_DISASTER_PIRATE_PETE,
                Quest.RECIPE_FOR_DISASTER_EVIL_DAVE,
                Quest.RECIPE_FOR_DISASTER_SKRACH_UGLOGWEE,
                Quest.RECIPE_FOR_DISASTER_WARTFACE_AND_BENTNOZE,
                Quest.RECIPE_FOR_DISASTER_DWARF
            ),
            
            ...this.questBoneVoyageCombat(),
            
            ...toAccountBuilder(TrainSkill.of(Skill.PRAYER, 43)),
            new DbGuester(Quest.ALFRED_GRIMHANDS_BARCRAWL, Quest.HORROR_FROM_THE_DEEP),
        ]
    }

    static accountTrainerGmaulerBegginingF2p() {
        return [
            ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, 25)),

            ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 5)),
            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 5)),

            ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 10)),
            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 10)),

            ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 20)),
            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 25)),
            ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 30)),
            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 30)),

            ...toAccountBuilder(TrainSkill.of(Skill.ATTACK, 37)),
            ...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, 40)),

            ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 25)),
            ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 25)),
            ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 25)),
            ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 25)),
            ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 25)),
            ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 25)),
            ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, 25)),

            ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 40)),

            ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 35)),
            ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 35)),
            ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 35)),
            ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 35)),
            ...toAccountBuilder(TrainSkill.of(Skill.SMITHING, 35)),
            ...toAccountBuilder(TrainSkill.of(Skill.FISHING, 35)),
            ...toAccountBuilder(TrainSkill.of(Skill.RUNECRAFTING, 30)),

            ...toAccountBuilder(TrainSkill.of(Skill.MAGIC, 50)),

            ...toAccountBuilder(TrainSkill.of(Skill.RANGE, 42)),
            ...toAccountBuilder(TrainSkill.of(Skill.FIREMAKING, 50)),
            ...toAccountBuilder(TrainSkill.of(Skill.CRAFTING, 42)),
            ...toAccountBuilder(TrainSkill.of(Skill.COOKING, 41)),
            ...toAccountBuilder(TrainSkill.of(Skill.WOODCUTTING, 36)),
        ]
    }

   
}


function accountTrainerCombatStats(attack: number, strength: number, defense: number) {
    const scripts: BotScript[] = []
    const maxLevel = Math.max(attack, strength, defense)
    
    // Train in 5-level increments, cycling through all skills
    for (let currentLevel = 5; currentLevel <= maxLevel; currentLevel += 5) {
        // Train attack if it needs this level
        if (attack >= currentLevel) {
            scripts.push(...toAccountBuilder(TrainSkill.of(Skill.ATTACK, Math.min(currentLevel, attack))))
        }
        
        // Train strength if it needs this level  
        if (strength >= currentLevel) {
            scripts.push(...toAccountBuilder(TrainSkill.of(Skill.STRENGTH, Math.min(currentLevel, strength))))
        }
        
        // Train defense if it needs this level
        if (defense >= currentLevel) {
            scripts.push(...toAccountBuilder(TrainSkill.of(Skill.DEFENSE, Math.min(currentLevel, defense))))
        }
    }
    
    return scripts
}


function rotatedSkiller(skills: Skill[], level: number) {
    const scripts: any[] = []
    const MAX_LEVEL_DIFFERENCE = 7 + Random.nextSeed(AccountData.seed(100), -3, 3)

    // Shuffle skills based on account seed for consistent randomness
    skills = Random.shuffle(skills, AccountData.seed(101))

    // Track current target levels for each skill
    const skillLevels: Map<Skill, number> = new Map()
    skills.forEach(skill => skillLevels.set(skill, 1))

    let seedOffset = 0

    // Continue training until all skills reach the target level
    while (Math.min(...Array.from(skillLevels.values())) < level) {
        // Find skills that can be trained (not at max level and within level difference constraint)
        const minLevel = Math.min(...Array.from(skillLevels.values()))
        const maxAllowedLevel = Math.min(minLevel + MAX_LEVEL_DIFFERENCE, level)

        const trainableSkills = skills.filter(skill => {
            const currentLevel = skillLevels.get(skill)!
            return currentLevel < level && currentLevel < maxAllowedLevel
        })

        if (trainableSkills.length === 0) {
            const lowestSkills = skills.filter(skill => skillLevels.get(skill) === minLevel)
            const skillToTrain = Random.nextItem(AccountData.seed() + seedOffset++, lowestSkills)
            const currentLevel = skillLevels.get(skillToTrain)!
            const targetLevel = Math.min(currentLevel + Random.nextSeed(AccountData.seed() + seedOffset++, 1, 5), level)

            scripts.push(...skillForRotatedScript(skillToTrain, targetLevel))
            skillLevels.set(skillToTrain, targetLevel)
        } else {
            // Randomly select a skill to train from trainable skills
            const skillToTrain = Random.nextItem(AccountData.seed(seedOffset++)  , trainableSkills)
            const currentLevel = skillLevels.get(skillToTrain)!

            const maxIncrease = Math.min(5, maxAllowedLevel - currentLevel, level - currentLevel)
            const levelIncrease = Random.nextSeed(AccountData.seed(seedOffset++), 1, Math.max(1, maxIncrease))
            const targetLevel = currentLevel + levelIncrease

            scripts.push(...skillForRotatedScript(skillToTrain, targetLevel))
            skillLevels.set(skillToTrain, targetLevel)
        }
    }

    // Ensure all skills reach exactly the target level
    for (const s of skills) {
        scripts.push(...skillForRotatedScript(s, level))
    }

    return scripts
}



function skill(skill: Skill, level: number) {
    if(skill == Skill.THIEVING) {
        return [
            new DbGuester(Quest.X_MARKS_THE_SPOT, Quest.CLIENT_OF_KOUREND),
            new Thiever().stopAtLevel(Skill.THIEVING, Math.min(49, level)),
            new StealingArtefacts().stopAtLevel(Skill.THIEVING, level)
        ]
    }

    return toAccountBuilder(TrainSkill.of(skill, level))
}

function skillForRotatedScript(s: Skill, level: number) {
    if(s == Skill.FARMING) {
        return [
            new BaggedPlants().stopAtLevel(Skill.FARMING,  Math.min(34, level)), 
            new TitheFarm().stopAtLevel(Skill.FARMING, level)
        ]
    }
    
    return skill(s, level)
}

function teleAlchTrainer(level: number) {
    return toAccountBuilder({ skill: Skill.MAGIC, trainUntilLevel: level, scripts: [new Alcher(99, true)] })
}

function quests(...quests: Quest[]) {
    return new DbGuester(...quests)
}


function prepareForSale(...type: PrepareAccType[]) {
    return type.map((t) => new PrepareForSale(t))
}